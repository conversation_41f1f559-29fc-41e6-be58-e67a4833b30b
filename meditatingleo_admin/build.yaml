# Build configuration for MeditatingLeo Admin Panel
# This file configures code generation for Riverpod, Freezed, and Drift

targets:
  $default:
    builders:
      # Riverpod code generation
      riverpod_generator:
        options:
          # Generate providers with proper naming
          provider_name_prefix: ""
          provider_name_suffix: "Provider"
        generate_for:
          - lib/**
          - test/**

      # Freezed code generation for immutable models
      freezed:
        generate_for:
          - lib/**
          - test/**

      # JSON serialization
      json_serializable:
        options:
          # Generate both toJson and fromJson methods
          any_map: false
          checked: false
          create_factory: true
          create_to_json: true
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true
        generate_for:
          - lib/**
          - test/**

      # Drift database code generation
      drift_dev:
        options:
          # Generate type-safe database code
          skip_verification_code: false
          use_data_class_name_for_companions: true
          generate_connect_constructor: true
          case_from_dart_to_sql: snake_case
        generate_for:
          - lib/**

# Global options
global_options:
  # Optimize build performance
  riverpod_generator:
    options:
      # Enable debug mode for development
      debug: false
      # Generate documentation
      generate_docs: true

  freezed:
    options:
      # Generate copyWith methods
      copy_with: true
      # Generate equality operators
      equal: true
      # Generate toString methods
      to_string: true
      # Generate when/map methods for unions
      map: true
      # Generate maybeMap/maybeWhen methods
      maybe_map: true

  json_serializable:
    options:
      # Use modern JSON serialization patterns
      create_factory: true
      create_to_json: true

  drift_dev:
    options:
      # Enable modern Drift features
      generate_connect_constructor: true
