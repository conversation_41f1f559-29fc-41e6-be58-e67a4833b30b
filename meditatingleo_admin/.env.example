# ClarityByMeditatingLeo Admin Panel Environment Configuration
# Copy this file to .env and fill in your actual values

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Admin Authentication (Required)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your_secure_admin_password_here

# Database Configuration (Required)
DATABASE_URL=postgresql://user:password@localhost:5432/meditatingleo_admin

# Analytics & Monitoring (Optional)
SENTRY_DSN=https://<EMAIL>/project-id
MIXPANEL_TOKEN=your_mixpanel_token_here

# File Storage (Required)
STORAGE_BUCKET=meditatingleo-admin-storage

# Security (Required)
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
ENCRYPTION_KEY=your_encryption_key_here_minimum_32_characters

# Development Settings
FLUTTER_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug

# Admin Panel Specific
ADMIN_PANEL_VERSION=1.0.0
ADMIN_PANEL_BUILD=1

# Content Management
MAX_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# System Monitoring
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# Localization
DEFAULT_LOCALE=en_US
SUPPORTED_LOCALES=en_US,es_ES,fr_FR

# Feature Flags
ENABLE_RICH_TEXT_EDITOR=true
ENABLE_BULK_OPERATIONS=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_REAL_TIME_MONITORING=true

# Rate Limiting
API_RATE_LIMIT=1000
ADMIN_RATE_LIMIT=5000

# Backup & Recovery
AUTO_BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# Compliance
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
AUDIT_LOG_RETENTION_DAYS=2555

# Performance
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000

# Admin Panel UI
THEME_MODE=system
SIDEBAR_COLLAPSED=false
DASHBOARD_REFRESH_INTERVAL=60000

# Content Delivery (Optional)
CDN_URL=https://cdn.meditatingleo.com
ASSET_BASE_URL=https://assets.meditatingleo.com

# Third-party Integrations (Optional)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# WebSocket Configuration (Optional)
WEBSOCKET_URL=wss://your-websocket-url.com
WEBSOCKET_RECONNECT_INTERVAL=5000

# Admin Notifications
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=false
NOTIFICATION_CHANNELS=email,in_app

# System Limits
MAX_USERS_PER_ADMIN=10000
MAX_CONTENT_ITEMS=50000
MAX_CONCURRENT_ADMIN_SESSIONS=10

# Development Tools
ENABLE_FLUTTER_INSPECTOR=true
ENABLE_PERFORMANCE_OVERLAY=false
ENABLE_DEBUG_BANNER=true

# Testing Configuration (Optional)
TEST_DATABASE_URL=postgresql://user:password@localhost:5432/meditatingleo_test
TEST_SUPABASE_URL=https://your-test-project.supabase.co
TEST_SUPABASE_ANON_KEY=your_test_supabase_anon_key

# Build Configuration
BUILD_MODE=debug
TARGET_PLATFORM=web
ENABLE_WEB_RENDERER=canvaskit

# Security Headers
ENABLE_CORS=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://admin.meditatingleo.com
ENABLE_CSP=true
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'

# Admin Panel Branding
APP_NAME=ClarityByMeditatingLeo Admin
APP_LOGO_URL=assets/images/admin_logo.png
COMPANY_NAME=MeditatingLeo
SUPPORT_EMAIL=<EMAIL>
SUPPORT_URL=https://support.meditatingleo.com

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1
