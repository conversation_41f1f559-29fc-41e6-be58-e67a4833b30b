import 'package:flutter_test/flutter_test.dart';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';
import 'package:matcher/matcher.dart' as matcher;

void main() {
  group('AdminDatabase', () {
    late AdminDatabase database;

    setUp(() {
      // Use in-memory database for testing
      database = AdminDatabase(NativeDatabase.memory());
    });

    tearDown(() async {
      await database.close();
    });

    group('Database Initialization', () {
      test('should initialize database successfully', () async {
        // Act & Assert
        expect(database, matcher.isNotNull);
        expect(database.schemaVersion, equals(1));
      });

      test('should create all required tables', () async {
        // Arrange
        final tables = database.allTables;

        // Assert
        expect(tables.length, greaterThan(0));
        expect(
            tables.any(
                (table) => table.actualTableName == 'journey_content_table'),
            isTrue);
        expect(tables.any((table) => table.actualTableName == 'admin_users'),
            isTrue);
        expect(tables.any((table) => table.actualTableName == 'system_metrics'),
            isTrue);
      });
    });

    group('Journey Content Operations', () {
      test('should insert journey content successfully', () async {
        // Arrange
        final journeyData = JourneyContentDataCompanion(
          id: const Value('test-journey-1'),
          title: const Value('Test Journey'),
          description: const Value('A test journey for clarity'),
          category: const Value('mindfulness'),
          difficulty: const Value('beginner'),
          prompts: const Value(['What are you grateful for today?']),
          isPublished: const Value(true),
          createdAt: Value(DateTime.now()),
          updatedAt: Value(DateTime.now()),
        );

        // Act
        final insertedId = await database
            .into(database.journeyContentTable)
            .insert(journeyData);

        // Assert
        expect(insertedId, matcher.isNotNull);

        final journeys =
            await database.select(database.journeyContentTable).get();
        expect(journeys.length, equals(1));
        expect(journeys.first.title, equals('Test Journey'));
      });

      test('should update journey content successfully', () async {
        // Arrange
        final journeyData = JourneyContentDataCompanion(
          id: const Value('test-journey-1'),
          title: const Value('Test Journey'),
          description: const Value('A test journey for clarity'),
          category: const Value('mindfulness'),
          difficulty: const Value('beginner'),
          prompts: const Value(['What are you grateful for today?']),
          isPublished: const Value(false),
          createdAt: Value(DateTime.now()),
          updatedAt: Value(DateTime.now()),
        );

        await database.into(database.journeyContentTable).insert(journeyData);

        // Act
        await (database.update(database.journeyContentTable)
              ..where((tbl) => tbl.id.equals('test-journey-1')))
            .write(const JourneyContentDataCompanion(
          title: Value('Updated Journey'),
          isPublished: Value(true),
        ));

        // Assert
        final journey = await (database.select(database.journeyContentTable)
              ..where((tbl) => tbl.id.equals('test-journey-1')))
            .getSingle();

        expect(journey.title, equals('Updated Journey'));
        expect(journey.isPublished, isTrue);
      });

      test('should delete journey content successfully', () async {
        // Arrange
        final journeyData = JourneyContentDataCompanion(
          id: const Value('test-journey-1'),
          title: const Value('Test Journey'),
          description: const Value('A test journey for clarity'),
          category: const Value('mindfulness'),
          difficulty: const Value('beginner'),
          prompts: const Value(['What are you grateful for today?']),
          isPublished: const Value(true),
          createdAt: Value(DateTime.now()),
          updatedAt: Value(DateTime.now()),
        );

        await database.into(database.journeyContentTable).insert(journeyData);

        // Act
        await (database.delete(database.journeyContentTable)
              ..where((tbl) => tbl.id.equals('test-journey-1')))
            .go();

        // Assert
        final journeys =
            await database.select(database.journeyContentTable).get();
        expect(journeys.length, equals(0));
      });
    });

    group('Admin User Operations', () {
      test('should insert admin user successfully', () async {
        // Arrange
        final adminData = AdminUserDataCompanion(
          id: const Value('admin-1'),
          email: const Value('<EMAIL>'),
          role: const Value('content_manager'),
          permissions: const Value(['content:create', 'content:edit']),
          isActive: const Value(true),
          lastLoginAt: Value(DateTime.now()),
          createdAt: Value(DateTime.now()),
        );

        // Act
        await database.into(database.adminUsers).insert(adminData);

        // Assert
        final admins = await database.select(database.adminUsers).get();
        expect(admins.length, equals(1));
        expect(admins.first.email, equals('<EMAIL>'));
        expect(admins.first.role, equals('content_manager'));
      });
    });

    group('System Metrics Operations', () {
      test('should insert system metrics successfully', () async {
        // Arrange
        final metricsData = SystemMetricsDataCompanion(
          id: const Value('metric-1'),
          metricType: const Value('user_count'),
          value: const Value(150.0),
          metadata: const Value({'platform': 'web', 'region': 'us-east'}),
          recordedAt: Value(DateTime.now()),
        );

        // Act
        await database.into(database.systemMetrics).insert(metricsData);

        // Assert
        final metrics = await database.select(database.systemMetrics).get();
        expect(metrics.length, equals(1));
        expect(metrics.first.metricType, equals('user_count'));
        expect(metrics.first.value, equals(150.0));
      });
    });
  });
}
