import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_admin/core/repositories/content_repository.dart';
import 'package:meditatingleo_admin/core/services/supabase_service.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';
import 'package:meditatingleo_admin/shared/models/journey_content.dart';
import 'package:meditatingleo_admin/shared/models/result.dart';

// Generate mocks
@GenerateMocks([SupabaseService, AdminDatabase])
import 'content_repository_test.mocks.dart';

void main() {
  group('ContentRepository', () {
    late ContentRepository repository;
    late MockSupabaseService mockSupabaseService;
    late MockAdminDatabase mockDatabase;

    setUp(() {
      mockSupabaseService = MockSupabaseService();
      mockDatabase = MockAdminDatabase();
      repository = ContentRepository(mockSupabaseService, mockDatabase);
    });

    group('Journey Content Management', () {
      test('should create journey content successfully', () async {
        // Arrange
        final journeyContent = JourneyContent(
          id: 'journey-1',
          title: 'Mindfulness Journey',
          description: 'A journey to mindfulness',
          category: 'mindfulness',
          difficulty: 'beginner',
          prompts: ['What are you grateful for?'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final mockResponse = {
          'id': 'journey-1',
          'title': 'Mindfulness Journey',
          'description': 'A journey to mindfulness',
          'category': 'mindfulness',
          'difficulty': 'beginner',
          'prompts': ['What are you grateful for?'],
          'is_published': false,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        when(mockSupabaseService.insert('journey_content', any))
            .thenAnswer((_) async => Result.success([mockResponse]));

        // Act
        final result = await repository.createJourneyContent(journeyContent);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.id, equals('journey-1'));
        expect(result.data?.title, equals('Mindfulness Journey'));
        verify(mockSupabaseService.insert('journey_content', any)).called(1);
      });

      test('should handle create journey content failure', () async {
        // Arrange
        final journeyContent = JourneyContent(
          id: 'journey-1',
          title: 'Mindfulness Journey',
          description: 'A journey to mindfulness',
          category: 'mindfulness',
          difficulty: 'beginner',
          prompts: ['What are you grateful for?'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        when(mockSupabaseService.insert('journey_content', any)).thenAnswer(
            (_) async => Result.failure(
                AppError.database('Failed to create journey content')));

        // Act
        final result = await repository.createJourneyContent(journeyContent);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error?.message,
            contains('Failed to create journey content'));
      });

      test('should get all journey content successfully', () async {
        // Arrange
        final mockData = [
          {
            'id': 'journey-1',
            'title': 'Journey 1',
            'description': 'Description 1',
            'category': 'mindfulness',
            'difficulty': 'beginner',
            'prompts': ['Prompt 1'],
            'is_published': true,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          {
            'id': 'journey-2',
            'title': 'Journey 2',
            'description': 'Description 2',
            'category': 'gratitude',
            'difficulty': 'intermediate',
            'prompts': ['Prompt 2'],
            'is_published': false,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
        ];

        when(mockSupabaseService.query('journey_content'))
            .thenAnswer((_) async => Result.success(mockData));

        // Act
        final result = await repository.getAllJourneyContent();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.length, equals(2));
        expect(result.data?.first.title, equals('Journey 1'));
        expect(result.data?.last.title, equals('Journey 2'));
        verify(mockSupabaseService.query('journey_content')).called(1);
      });

      test('should get journey content by id successfully', () async {
        // Arrange
        const journeyId = 'journey-1';
        final mockData = [
          {
            'id': 'journey-1',
            'title': 'Journey 1',
            'description': 'Description 1',
            'category': 'mindfulness',
            'difficulty': 'beginner',
            'prompts': ['Prompt 1'],
            'is_published': true,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          }
        ];

        when(mockSupabaseService.queryById('journey_content', journeyId))
            .thenAnswer((_) async => Result.success(mockData));

        // Act
        final result = await repository.getJourneyContentById(journeyId);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.id, equals('journey-1'));
        expect(result.data?.title, equals('Journey 1'));
        verify(mockSupabaseService.queryById('journey_content', journeyId))
            .called(1);
      });

      test('should update journey content successfully', () async {
        // Arrange
        final updatedJourney = JourneyContent(
          id: 'journey-1',
          title: 'Updated Journey',
          description: 'Updated description',
          category: 'mindfulness',
          difficulty: 'beginner',
          prompts: ['Updated prompt'],
          isPublished: true,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now(),
        );

        final mockResponse = [
          {
            'id': 'journey-1',
            'title': 'Updated Journey',
            'description': 'Updated description',
            'category': 'mindfulness',
            'difficulty': 'beginner',
            'prompts': ['Updated prompt'],
            'is_published': true,
            'created_at': DateTime.now()
                .subtract(const Duration(days: 1))
                .toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          }
        ];

        when(mockSupabaseService.update('journey_content', 'journey-1', any))
            .thenAnswer((_) async => Result.success(mockResponse));

        // Act
        final result = await repository.updateJourneyContent(updatedJourney);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.title, equals('Updated Journey'));
        expect(result.data?.isPublished, isTrue);
        verify(mockSupabaseService.update('journey_content', 'journey-1', any))
            .called(1);
      });

      test('should delete journey content successfully', () async {
        // Arrange
        const journeyId = 'journey-1';

        when(mockSupabaseService.delete('journey_content', journeyId))
            .thenAnswer((_) async => Result.success(null));

        // Act
        final result = await repository.deleteJourneyContent(journeyId);

        // Assert
        expect(result.isSuccess, isTrue);
        verify(mockSupabaseService.delete('journey_content', journeyId))
            .called(1);
      });

      test('should publish journey content successfully', () async {
        // Arrange
        const journeyId = 'journey-1';
        final mockResponse = [
          {
            'id': 'journey-1',
            'title': 'Journey 1',
            'description': 'Description 1',
            'category': 'mindfulness',
            'difficulty': 'beginner',
            'prompts': ['Prompt 1'],
            'is_published': true,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          }
        ];

        when(mockSupabaseService.update('journey_content', journeyId, any))
            .thenAnswer((_) async => Result.success(mockResponse));

        // Act
        final result = await repository.publishJourneyContent(journeyId);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.isPublished, isTrue);
        verify(mockSupabaseService.update('journey_content', journeyId, any))
            .called(1);
      });

      test('should unpublish journey content successfully', () async {
        // Arrange
        const journeyId = 'journey-1';
        final mockResponse = [
          {
            'id': 'journey-1',
            'title': 'Journey 1',
            'description': 'Description 1',
            'category': 'mindfulness',
            'difficulty': 'beginner',
            'prompts': ['Prompt 1'],
            'is_published': false,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          }
        ];

        when(mockSupabaseService.update('journey_content', journeyId, any))
            .thenAnswer((_) async => Result.success(mockResponse));

        // Act
        final result = await repository.unpublishJourneyContent(journeyId);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?.isPublished, isFalse);
        verify(mockSupabaseService.update('journey_content', journeyId, any))
            .called(1);
      });
    });

    group('Content Analytics', () {
      test('should get content analytics successfully', () async {
        // Arrange
        final mockAnalytics = {
          'total_journeys': 25,
          'published_journeys': 20,
          'draft_journeys': 5,
          'categories': {
            'mindfulness': 10,
            'gratitude': 8,
            'reflection': 7,
          },
          'difficulty_distribution': {
            'beginner': 12,
            'intermediate': 8,
            'advanced': 5,
          },
        };

        when(mockSupabaseService.rpc('get_content_analytics', {}))
            .thenAnswer((_) async => Result.success(mockAnalytics));

        // Act
        final result = await repository.getContentAnalytics();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.data?['total_journeys'], equals(25));
        expect(result.data?['published_journeys'], equals(20));
        verify(mockSupabaseService.rpc('get_content_analytics', {})).called(1);
      });
    });
  });
}
