// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/core/repositories/content_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:drift/drift.dart' as _i3;
import 'package:drift/src/runtime/executor/stream_queries.dart' as _i5;
import 'package:meditatingleo_admin/core/database/admin_database.dart' as _i4;
import 'package:meditatingleo_admin/core/services/supabase_service.dart' as _i7;
import 'package:meditatingleo_admin/shared/models/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;
import 'package:supabase_flutter/supabase_flutter.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T, E> extends _i1.SmartFake implements _i2.Result<T, E> {
  _FakeResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrationStrategy_1 extends _i1.SmartFake
    implements _i3.MigrationStrategy {
  _FakeMigrationStrategy_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$JourneyContentTableTable_2 extends _i1.SmartFake
    implements _i4.$JourneyContentTableTable {
  _Fake$JourneyContentTableTable_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminUsersTable_3 extends _i1.SmartFake
    implements _i4.$AdminUsersTable {
  _Fake$AdminUsersTable_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$SystemMetricsTable_4 extends _i1.SmartFake
    implements _i4.$SystemMetricsTable {
  _Fake$SystemMetricsTable_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminDatabaseManager_5 extends _i1.SmartFake
    implements _i4.$AdminDatabaseManager {
  _Fake$AdminDatabaseManager_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGeneratedDatabase_6 extends _i1.SmartFake
    implements _i3.GeneratedDatabase {
  _FakeGeneratedDatabase_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDriftDatabaseOptions_7 extends _i1.SmartFake
    implements _i3.DriftDatabaseOptions {
  _FakeDriftDatabaseOptions_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryUpdateRules_8 extends _i1.SmartFake
    implements _i3.StreamQueryUpdateRules {
  _FakeStreamQueryUpdateRules_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnection_9 extends _i1.SmartFake
    implements _i3.DatabaseConnection {
  _FakeDatabaseConnection_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQueryExecutor_10 extends _i1.SmartFake implements _i3.QueryExecutor {
  _FakeQueryExecutor_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryStore_11 extends _i1.SmartFake
    implements _i5.StreamQueryStore {
  _FakeStreamQueryStore_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnectionUser_12 extends _i1.SmartFake
    implements _i3.DatabaseConnectionUser {
  _FakeDatabaseConnectionUser_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrator_13 extends _i1.SmartFake implements _i3.Migrator {
  _FakeMigrator_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_14<T1> extends _i1.SmartFake implements _i6.Future<T1> {
  _FakeFuture_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInsertStatement_15<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.InsertStatement<T1, D1> {
  _FakeInsertStatement_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUpdateStatement_16<T extends _i3.Table, D> extends _i1.SmartFake
    implements _i3.UpdateStatement<T, D> {
  _FakeUpdateStatement_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSimpleSelectStatement_17<T1 extends _i3.HasResultSet, D>
    extends _i1.SmartFake implements _i3.SimpleSelectStatement<T1, D> {
  _FakeSimpleSelectStatement_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeJoinedSelectStatement_18<FirstT extends _i3.HasResultSet, FirstD>
    extends _i1.SmartFake implements _i3.JoinedSelectStatement<FirstT, FirstD> {
  _FakeJoinedSelectStatement_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseSelectStatement_19<Row> extends _i1.SmartFake
    implements _i3.BaseSelectStatement<Row> {
  _FakeBaseSelectStatement_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeleteStatement_20<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.DeleteStatement<T1, D1> {
  _FakeDeleteStatement_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSelectable_21<T> extends _i1.SmartFake implements _i3.Selectable<T> {
  _FakeSelectable_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGenerationContext_22 extends _i1.SmartFake
    implements _i3.GenerationContext {
  _FakeGenerationContext_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseService extends _i1.Mock implements _i7.SupabaseService {
  MockSupabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i8.AuthState> get authStateStream => (super.noSuchMethod(
        Invocation.getter(#authStateStream),
        returnValue: _i6.Stream<_i8.AuthState>.empty(),
      ) as _i6.Stream<_i8.AuthState>);

  @override
  _i6.Future<_i2.Result<_i8.AuthResponse, _i2.AppError>> signInWithEmail(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmail,
          [
            email,
            password,
          ],
        ),
        returnValue:
            _i6.Future<_i2.Result<_i8.AuthResponse, _i2.AppError>>.value(
                _FakeResult_0<_i8.AuthResponse, _i2.AppError>(
          this,
          Invocation.method(
            #signInWithEmail,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<_i8.AuthResponse, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<void, _i2.AppError>> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i6.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #signOut,
            [],
          ),
        )),
      ) as _i6.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> query(
          String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [tableName],
        ),
        returnValue: _i6
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #query,
            [tableName],
          ),
        )),
      ) as _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> queryById(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryById,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i6
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #queryById,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> insert(
    String? tableName,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            tableName,
            data,
          ],
        ),
        returnValue: _i6
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #insert,
            [
              tableName,
              data,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> update(
    String? tableName,
    String? id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            tableName,
            id,
            data,
          ],
        ),
        returnValue: _i6
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #update,
            [
              tableName,
              id,
              data,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<void, _i2.AppError>> delete(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i6.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #delete,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i6.Future<_i2.Result<dynamic, _i2.AppError>> rpc(
    String? functionName,
    Map<String, dynamic>? params,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [
            functionName,
            params,
          ],
        ),
        returnValue: _i6.Future<_i2.Result<dynamic, _i2.AppError>>.value(
            _FakeResult_0<dynamic, _i2.AppError>(
          this,
          Invocation.method(
            #rpc,
            [
              functionName,
              params,
            ],
          ),
        )),
      ) as _i6.Future<_i2.Result<dynamic, _i2.AppError>>);

  @override
  _i6.Stream<Map<String, dynamic>> subscribe(String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribe,
          [tableName],
        ),
        returnValue: _i6.Stream<Map<String, dynamic>>.empty(),
      ) as _i6.Stream<Map<String, dynamic>>);

  @override
  bool hasAdminPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasAdminPermissions,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  List<String> getUserPermissions() => (super.noSuchMethod(
        Invocation.method(
          #getUserPermissions,
          [],
        ),
        returnValue: <String>[],
      ) as List<String>);
}

/// A class which mocks [AdminDatabase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminDatabase extends _i1.Mock implements _i4.AdminDatabase {
  MockAdminDatabase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get schemaVersion => (super.noSuchMethod(
        Invocation.getter(#schemaVersion),
        returnValue: 0,
      ) as int);

  @override
  _i3.MigrationStrategy get migration => (super.noSuchMethod(
        Invocation.getter(#migration),
        returnValue: _FakeMigrationStrategy_1(
          this,
          Invocation.getter(#migration),
        ),
      ) as _i3.MigrationStrategy);

  @override
  _i4.$JourneyContentTableTable get journeyContentTable => (super.noSuchMethod(
        Invocation.getter(#journeyContentTable),
        returnValue: _Fake$JourneyContentTableTable_2(
          this,
          Invocation.getter(#journeyContentTable),
        ),
      ) as _i4.$JourneyContentTableTable);

  @override
  _i4.$AdminUsersTable get adminUsers => (super.noSuchMethod(
        Invocation.getter(#adminUsers),
        returnValue: _Fake$AdminUsersTable_3(
          this,
          Invocation.getter(#adminUsers),
        ),
      ) as _i4.$AdminUsersTable);

  @override
  _i4.$SystemMetricsTable get systemMetrics => (super.noSuchMethod(
        Invocation.getter(#systemMetrics),
        returnValue: _Fake$SystemMetricsTable_4(
          this,
          Invocation.getter(#systemMetrics),
        ),
      ) as _i4.$SystemMetricsTable);

  @override
  _i4.$AdminDatabaseManager get managers => (super.noSuchMethod(
        Invocation.getter(#managers),
        returnValue: _Fake$AdminDatabaseManager_5(
          this,
          Invocation.getter(#managers),
        ),
      ) as _i4.$AdminDatabaseManager);

  @override
  Iterable<_i3.TableInfo<_i3.Table, Object?>> get allTables =>
      (super.noSuchMethod(
        Invocation.getter(#allTables),
        returnValue: <_i3.TableInfo<_i3.Table, Object?>>[],
      ) as Iterable<_i3.TableInfo<_i3.Table, Object?>>);

  @override
  List<_i3.DatabaseSchemaEntity> get allSchemaEntities => (super.noSuchMethod(
        Invocation.getter(#allSchemaEntities),
        returnValue: <_i3.DatabaseSchemaEntity>[],
      ) as List<_i3.DatabaseSchemaEntity>);

  @override
  _i3.GeneratedDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeGeneratedDatabase_6(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i3.GeneratedDatabase);

  @override
  _i3.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_7(
          this,
          Invocation.getter(#options),
        ),
      ) as _i3.DriftDatabaseOptions);

  @override
  _i3.StreamQueryUpdateRules get streamUpdateRules => (super.noSuchMethod(
        Invocation.getter(#streamUpdateRules),
        returnValue: _FakeStreamQueryUpdateRules_8(
          this,
          Invocation.getter(#streamUpdateRules),
        ),
      ) as _i3.StreamQueryUpdateRules);

  @override
  _i3.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_9(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i3.DatabaseConnection);

  @override
  _i3.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i9.dummyValue<_i3.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i3.SqlTypes);

  @override
  _i3.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_10(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i3.QueryExecutor);

  @override
  _i5.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_11(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i5.StreamQueryStore);

  @override
  _i3.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_12(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i3.DatabaseConnectionUser);

  @override
  _i6.Future<int> insertJourneyContent(
          _i4.JourneyContentDataCompanion? journey) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertJourneyContent,
          [journey],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.JourneyContentData>> getAllJourneyContent() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllJourneyContent,
          [],
        ),
        returnValue: _i6.Future<List<_i4.JourneyContentData>>.value(
            <_i4.JourneyContentData>[]),
      ) as _i6.Future<List<_i4.JourneyContentData>>);

  @override
  _i6.Future<_i4.JourneyContentData?> getJourneyContentById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getJourneyContentById,
          [id],
        ),
        returnValue: _i6.Future<_i4.JourneyContentData?>.value(),
      ) as _i6.Future<_i4.JourneyContentData?>);

  @override
  _i6.Future<bool> updateJourneyContent(
    String? id,
    _i4.JourneyContentDataCompanion? journey,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateJourneyContent,
          [
            id,
            journey,
          ],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteJourneyContent(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteJourneyContent,
          [id],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<int> insertAdminUser(_i4.AdminUserDataCompanion? user) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAdminUser,
          [user],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.AdminUserData>> getAllAdminUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllAdminUsers,
          [],
        ),
        returnValue:
            _i6.Future<List<_i4.AdminUserData>>.value(<_i4.AdminUserData>[]),
      ) as _i6.Future<List<_i4.AdminUserData>>);

  @override
  _i6.Future<_i4.AdminUserData?> getAdminUserById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAdminUserById,
          [id],
        ),
        returnValue: _i6.Future<_i4.AdminUserData?>.value(),
      ) as _i6.Future<_i4.AdminUserData?>);

  @override
  _i6.Future<int> insertSystemMetrics(
          _i4.SystemMetricsDataCompanion? metrics) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertSystemMetrics,
          [metrics],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.SystemMetricsData>> getAllSystemMetrics() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllSystemMetrics,
          [],
        ),
        returnValue: _i6.Future<List<_i4.SystemMetricsData>>.value(
            <_i4.SystemMetricsData>[]),
      ) as _i6.Future<List<_i4.SystemMetricsData>>);

  @override
  _i6.Future<List<_i4.SystemMetricsData>> getSystemMetricsByType(
          String? metricType) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSystemMetricsByType,
          [metricType],
        ),
        returnValue: _i6.Future<List<_i4.SystemMetricsData>>.value(
            <_i4.SystemMetricsData>[]),
      ) as _i6.Future<List<_i4.SystemMetricsData>>);

  @override
  _i3.Migrator createMigrator() => (super.noSuchMethod(
        Invocation.method(
          #createMigrator,
          [],
        ),
        returnValue: _FakeMigrator_13(
          this,
          Invocation.method(
            #createMigrator,
            [],
          ),
        ),
      ) as _i3.Migrator);

  @override
  _i6.Future<void> beforeOpen(
    _i3.QueryExecutor? executor,
    _i3.OpeningDetails? details,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #beforeOpen,
          [
            executor,
            details,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Stream<T> createStream<T extends Object>(
          _i5.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i6.Stream<T>.empty(),
      ) as _i6.Stream<T>);

  @override
  T alias<T, D>(
    _i3.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i9.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i3.TableInfo<_i3.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i3.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Stream<Set<_i3.TableUpdate>> tableUpdates(
          [_i3.TableUpdateQuery? query = const _i3.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i6.Stream<Set<_i3.TableUpdate>>.empty(),
      ) as _i6.Stream<Set<_i3.TableUpdate>>);

  @override
  _i6.Future<T> doWhenOpened<T>(
          _i6.FutureOr<T> Function(_i3.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_14<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i3.InsertStatement<T, D> into<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_15<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i3.InsertStatement<T, D>);

  @override
  _i3.UpdateStatement<Tbl, R> update<Tbl extends _i3.Table, R>(
          _i3.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_16<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i3.UpdateStatement<Tbl, R>);

  @override
  _i3.SimpleSelectStatement<T, R> select<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_17<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.SimpleSelectStatement<T, R>);

  @override
  _i3.JoinedSelectStatement<T, R> selectOnly<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_18<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.JoinedSelectStatement<T, R>);

  @override
  _i3.BaseSelectStatement<_i3.TypedResult> selectExpressions(
          Iterable<_i3.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_19<_i3.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i3.BaseSelectStatement<_i3.TypedResult>);

  @override
  _i3.DeleteStatement<T, D> delete<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_20<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i3.DeleteStatement<T, D>);

  @override
  _i6.Future<int> customUpdate(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<int> customInsert(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i3.QueryRow>> customWriteReturning(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i6.Future<List<_i3.QueryRow>>.value(<_i3.QueryRow>[]),
      ) as _i6.Future<List<_i3.QueryRow>>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelect(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_21<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelectQuery(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_21<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i6.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<T> transaction<T>(
    _i6.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_14<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i6.Future<T> exclusively<T>(_i6.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_14<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i6.Future<void> batch(_i6.FutureOr<void> Function(_i3.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<T> runWithInterceptor<T>(
    _i6.Future<T> Function()? action, {
    required _i3.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i9.ifNotNull(
              _i9.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_14<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i3.GenerationContext $write(
    _i3.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_22(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  _i3.GenerationContext $writeInsertable(
    _i3.TableInfo<_i3.Table, dynamic>? table,
    _i3.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_22(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i9.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);
}
