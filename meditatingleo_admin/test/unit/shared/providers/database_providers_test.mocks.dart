// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/shared/providers/database_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:drift/drift.dart' as _i3;
import 'package:drift/src/runtime/executor/stream_queries.dart' as _i5;
import 'package:meditatingleo_admin/core/database/admin_database.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrationStrategy_9 extends _i1.SmartFake
    implements _i3.MigrationStrategy {
  _FakeMigrationStrategy_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$JourneyContentTableTable_10 extends _i1.SmartFake
    implements _i4.$JourneyContentTableTable {
  _Fake$JourneyContentTableTable_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminUsersTable_11 extends _i1.SmartFake
    implements _i4.$AdminUsersTable {
  _Fake$AdminUsersTable_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$SystemMetricsTable_12 extends _i1.SmartFake
    implements _i4.$SystemMetricsTable {
  _Fake$SystemMetricsTable_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _Fake$AdminDatabaseManager_13 extends _i1.SmartFake
    implements _i4.$AdminDatabaseManager {
  _Fake$AdminDatabaseManager_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGeneratedDatabase_14 extends _i1.SmartFake
    implements _i3.GeneratedDatabase {
  _FakeGeneratedDatabase_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDriftDatabaseOptions_15 extends _i1.SmartFake
    implements _i3.DriftDatabaseOptions {
  _FakeDriftDatabaseOptions_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryUpdateRules_16 extends _i1.SmartFake
    implements _i3.StreamQueryUpdateRules {
  _FakeStreamQueryUpdateRules_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnection_17 extends _i1.SmartFake
    implements _i3.DatabaseConnection {
  _FakeDatabaseConnection_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQueryExecutor_18 extends _i1.SmartFake implements _i3.QueryExecutor {
  _FakeQueryExecutor_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeStreamQueryStore_19 extends _i1.SmartFake
    implements _i5.StreamQueryStore {
  _FakeStreamQueryStore_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDatabaseConnectionUser_20 extends _i1.SmartFake
    implements _i3.DatabaseConnectionUser {
  _FakeDatabaseConnectionUser_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMigrator_21 extends _i1.SmartFake implements _i3.Migrator {
  _FakeMigrator_21(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_22<T1> extends _i1.SmartFake implements _i6.Future<T1> {
  _FakeFuture_22(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeInsertStatement_23<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.InsertStatement<T1, D1> {
  _FakeInsertStatement_23(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUpdateStatement_24<T extends _i3.Table, D> extends _i1.SmartFake
    implements _i3.UpdateStatement<T, D> {
  _FakeUpdateStatement_24(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSimpleSelectStatement_25<T1 extends _i3.HasResultSet, D>
    extends _i1.SmartFake implements _i3.SimpleSelectStatement<T1, D> {
  _FakeSimpleSelectStatement_25(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeJoinedSelectStatement_26<FirstT extends _i3.HasResultSet, FirstD>
    extends _i1.SmartFake implements _i3.JoinedSelectStatement<FirstT, FirstD> {
  _FakeJoinedSelectStatement_26(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBaseSelectStatement_27<Row> extends _i1.SmartFake
    implements _i3.BaseSelectStatement<Row> {
  _FakeBaseSelectStatement_27(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDeleteStatement_28<T1 extends _i3.Table, D1> extends _i1.SmartFake
    implements _i3.DeleteStatement<T1, D1> {
  _FakeDeleteStatement_28(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSelectable_29<T> extends _i1.SmartFake implements _i3.Selectable<T> {
  _FakeSelectable_29(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGenerationContext_30 extends _i1.SmartFake
    implements _i3.GenerationContext {
  _FakeGenerationContext_30(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions => (super.noSuchMethod(
        Invocation.getter(#functions),
        returnValue: _FakeFunctionsClient_0(
          this,
          Invocation.getter(#functions),
        ),
      ) as _i2.FunctionsClient);

  @override
  _i2.SupabaseStorageClient get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeSupabaseStorageClient_1(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i2.SupabaseStorageClient);

  @override
  _i2.RealtimeClient get realtime => (super.noSuchMethod(
        Invocation.getter(#realtime),
        returnValue: _FakeRealtimeClient_2(
          this,
          Invocation.getter(#realtime),
        ),
      ) as _i2.RealtimeClient);

  @override
  _i2.PostgrestClient get rest => (super.noSuchMethod(
        Invocation.getter(#rest),
        returnValue: _FakePostgrestClient_3(
          this,
          Invocation.getter(#rest),
        ),
      ) as _i2.PostgrestClient);

  @override
  Map<String, String> get headers => (super.noSuchMethod(
        Invocation.getter(#headers),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  _i2.GoTrueClient get auth => (super.noSuchMethod(
        Invocation.getter(#auth),
        returnValue: _FakeGoTrueClient_4(
          this,
          Invocation.getter(#auth),
        ),
      ) as _i2.GoTrueClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
        Invocation.setter(
          #functions,
          _functions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
        Invocation.setter(
          #storage,
          _storage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
        Invocation.setter(
          #realtime,
          _realtime,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
        Invocation.setter(
          #rest,
          _rest,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
        Invocation.setter(
          #headers,
          headers,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.SupabaseQueryBuilder from(String? table) => (super.noSuchMethod(
        Invocation.method(
          #from,
          [table],
        ),
        returnValue: _FakeSupabaseQueryBuilder_5(
          this,
          Invocation.method(
            #from,
            [table],
          ),
        ),
      ) as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) => (super.noSuchMethod(
        Invocation.method(
          #schema,
          [schema],
        ),
        returnValue: _FakeSupabaseQuerySchema_6(
          this,
          Invocation.method(
            #schema,
            [schema],
          ),
        ),
      ) as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [fn],
          {
            #params: params,
            #get: get,
          },
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rpc,
            [fn],
            {
              #params: params,
              #get: get,
            },
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #channel,
          [name],
          {#opts: opts},
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #channel,
            [name],
            {#opts: opts},
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() => (super.noSuchMethod(
        Invocation.method(
          #getChannels,
          [],
        ),
        returnValue: <_i2.RealtimeChannel>[],
      ) as List<_i2.RealtimeChannel>);

  @override
  _i6.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeChannel,
          [channel],
        ),
        returnValue: _i6.Future<String>.value(_i7.dummyValue<String>(
          this,
          Invocation.method(
            #removeChannel,
            [channel],
          ),
        )),
      ) as _i6.Future<String>);

  @override
  _i6.Future<List<String>> removeAllChannels() => (super.noSuchMethod(
        Invocation.method(
          #removeAllChannels,
          [],
        ),
        returnValue: _i6.Future<List<String>>.value(<String>[]),
      ) as _i6.Future<List<String>>);

  @override
  _i6.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

/// A class which mocks [AdminDatabase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminDatabase extends _i1.Mock implements _i4.AdminDatabase {
  MockAdminDatabase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get schemaVersion => (super.noSuchMethod(
        Invocation.getter(#schemaVersion),
        returnValue: 0,
      ) as int);

  @override
  _i3.MigrationStrategy get migration => (super.noSuchMethod(
        Invocation.getter(#migration),
        returnValue: _FakeMigrationStrategy_9(
          this,
          Invocation.getter(#migration),
        ),
      ) as _i3.MigrationStrategy);

  @override
  _i4.$JourneyContentTableTable get journeyContentTable => (super.noSuchMethod(
        Invocation.getter(#journeyContentTable),
        returnValue: _Fake$JourneyContentTableTable_10(
          this,
          Invocation.getter(#journeyContentTable),
        ),
      ) as _i4.$JourneyContentTableTable);

  @override
  _i4.$AdminUsersTable get adminUsers => (super.noSuchMethod(
        Invocation.getter(#adminUsers),
        returnValue: _Fake$AdminUsersTable_11(
          this,
          Invocation.getter(#adminUsers),
        ),
      ) as _i4.$AdminUsersTable);

  @override
  _i4.$SystemMetricsTable get systemMetrics => (super.noSuchMethod(
        Invocation.getter(#systemMetrics),
        returnValue: _Fake$SystemMetricsTable_12(
          this,
          Invocation.getter(#systemMetrics),
        ),
      ) as _i4.$SystemMetricsTable);

  @override
  _i4.$AdminDatabaseManager get managers => (super.noSuchMethod(
        Invocation.getter(#managers),
        returnValue: _Fake$AdminDatabaseManager_13(
          this,
          Invocation.getter(#managers),
        ),
      ) as _i4.$AdminDatabaseManager);

  @override
  Iterable<_i3.TableInfo<_i3.Table, Object?>> get allTables =>
      (super.noSuchMethod(
        Invocation.getter(#allTables),
        returnValue: <_i3.TableInfo<_i3.Table, Object?>>[],
      ) as Iterable<_i3.TableInfo<_i3.Table, Object?>>);

  @override
  List<_i3.DatabaseSchemaEntity> get allSchemaEntities => (super.noSuchMethod(
        Invocation.getter(#allSchemaEntities),
        returnValue: <_i3.DatabaseSchemaEntity>[],
      ) as List<_i3.DatabaseSchemaEntity>);

  @override
  _i3.GeneratedDatabase get attachedDatabase => (super.noSuchMethod(
        Invocation.getter(#attachedDatabase),
        returnValue: _FakeGeneratedDatabase_14(
          this,
          Invocation.getter(#attachedDatabase),
        ),
      ) as _i3.GeneratedDatabase);

  @override
  _i3.DriftDatabaseOptions get options => (super.noSuchMethod(
        Invocation.getter(#options),
        returnValue: _FakeDriftDatabaseOptions_15(
          this,
          Invocation.getter(#options),
        ),
      ) as _i3.DriftDatabaseOptions);

  @override
  _i3.StreamQueryUpdateRules get streamUpdateRules => (super.noSuchMethod(
        Invocation.getter(#streamUpdateRules),
        returnValue: _FakeStreamQueryUpdateRules_16(
          this,
          Invocation.getter(#streamUpdateRules),
        ),
      ) as _i3.StreamQueryUpdateRules);

  @override
  _i3.DatabaseConnection get connection => (super.noSuchMethod(
        Invocation.getter(#connection),
        returnValue: _FakeDatabaseConnection_17(
          this,
          Invocation.getter(#connection),
        ),
      ) as _i3.DatabaseConnection);

  @override
  _i3.SqlTypes get typeMapping => (super.noSuchMethod(
        Invocation.getter(#typeMapping),
        returnValue: _i7.dummyValue<_i3.SqlTypes>(
          this,
          Invocation.getter(#typeMapping),
        ),
      ) as _i3.SqlTypes);

  @override
  _i3.QueryExecutor get executor => (super.noSuchMethod(
        Invocation.getter(#executor),
        returnValue: _FakeQueryExecutor_18(
          this,
          Invocation.getter(#executor),
        ),
      ) as _i3.QueryExecutor);

  @override
  _i5.StreamQueryStore get streamQueries => (super.noSuchMethod(
        Invocation.getter(#streamQueries),
        returnValue: _FakeStreamQueryStore_19(
          this,
          Invocation.getter(#streamQueries),
        ),
      ) as _i5.StreamQueryStore);

  @override
  _i3.DatabaseConnectionUser get resolvedEngine => (super.noSuchMethod(
        Invocation.getter(#resolvedEngine),
        returnValue: _FakeDatabaseConnectionUser_20(
          this,
          Invocation.getter(#resolvedEngine),
        ),
      ) as _i3.DatabaseConnectionUser);

  @override
  _i6.Future<int> insertJourneyContent(
          _i4.JourneyContentDataCompanion? journey) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertJourneyContent,
          [journey],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.JourneyContentData>> getAllJourneyContent() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllJourneyContent,
          [],
        ),
        returnValue: _i6.Future<List<_i4.JourneyContentData>>.value(
            <_i4.JourneyContentData>[]),
      ) as _i6.Future<List<_i4.JourneyContentData>>);

  @override
  _i6.Future<_i4.JourneyContentData?> getJourneyContentById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getJourneyContentById,
          [id],
        ),
        returnValue: _i6.Future<_i4.JourneyContentData?>.value(),
      ) as _i6.Future<_i4.JourneyContentData?>);

  @override
  _i6.Future<bool> updateJourneyContent(
    String? id,
    _i4.JourneyContentDataCompanion? journey,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateJourneyContent,
          [
            id,
            journey,
          ],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> deleteJourneyContent(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteJourneyContent,
          [id],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<int> insertAdminUser(_i4.AdminUserDataCompanion? user) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertAdminUser,
          [user],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.AdminUserData>> getAllAdminUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllAdminUsers,
          [],
        ),
        returnValue:
            _i6.Future<List<_i4.AdminUserData>>.value(<_i4.AdminUserData>[]),
      ) as _i6.Future<List<_i4.AdminUserData>>);

  @override
  _i6.Future<_i4.AdminUserData?> getAdminUserById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAdminUserById,
          [id],
        ),
        returnValue: _i6.Future<_i4.AdminUserData?>.value(),
      ) as _i6.Future<_i4.AdminUserData?>);

  @override
  _i6.Future<int> insertSystemMetrics(
          _i4.SystemMetricsDataCompanion? metrics) =>
      (super.noSuchMethod(
        Invocation.method(
          #insertSystemMetrics,
          [metrics],
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i4.SystemMetricsData>> getAllSystemMetrics() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllSystemMetrics,
          [],
        ),
        returnValue: _i6.Future<List<_i4.SystemMetricsData>>.value(
            <_i4.SystemMetricsData>[]),
      ) as _i6.Future<List<_i4.SystemMetricsData>>);

  @override
  _i6.Future<List<_i4.SystemMetricsData>> getSystemMetricsByType(
          String? metricType) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSystemMetricsByType,
          [metricType],
        ),
        returnValue: _i6.Future<List<_i4.SystemMetricsData>>.value(
            <_i4.SystemMetricsData>[]),
      ) as _i6.Future<List<_i4.SystemMetricsData>>);

  @override
  _i3.Migrator createMigrator() => (super.noSuchMethod(
        Invocation.method(
          #createMigrator,
          [],
        ),
        returnValue: _FakeMigrator_21(
          this,
          Invocation.method(
            #createMigrator,
            [],
          ),
        ),
      ) as _i3.Migrator);

  @override
  _i6.Future<void> beforeOpen(
    _i3.QueryExecutor? executor,
    _i3.OpeningDetails? details,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #beforeOpen,
          [
            executor,
            details,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Stream<T> createStream<T extends Object>(
          _i5.QueryStreamFetcher<T>? stmt) =>
      (super.noSuchMethod(
        Invocation.method(
          #createStream,
          [stmt],
        ),
        returnValue: _i6.Stream<T>.empty(),
      ) as _i6.Stream<T>);

  @override
  T alias<T, D>(
    _i3.ResultSetImplementation<T, D>? table,
    String? alias,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #alias,
          [
            table,
            alias,
          ],
        ),
        returnValue: _i7.dummyValue<T>(
          this,
          Invocation.method(
            #alias,
            [
              table,
              alias,
            ],
          ),
        ),
      ) as T);

  @override
  void markTablesUpdated(Iterable<_i3.TableInfo<_i3.Table, dynamic>>? tables) =>
      super.noSuchMethod(
        Invocation.method(
          #markTablesUpdated,
          [tables],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyUpdates(Set<_i3.TableUpdate>? updates) => super.noSuchMethod(
        Invocation.method(
          #notifyUpdates,
          [updates],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Stream<Set<_i3.TableUpdate>> tableUpdates(
          [_i3.TableUpdateQuery? query = const _i3.TableUpdateQuery.any()]) =>
      (super.noSuchMethod(
        Invocation.method(
          #tableUpdates,
          [query],
        ),
        returnValue: _i6.Stream<Set<_i3.TableUpdate>>.empty(),
      ) as _i6.Stream<Set<_i3.TableUpdate>>);

  @override
  _i6.Future<T> doWhenOpened<T>(
          _i6.FutureOr<T> Function(_i3.QueryExecutor)? fn) =>
      (super.noSuchMethod(
        Invocation.method(
          #doWhenOpened,
          [fn],
        ),
        returnValue: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #doWhenOpened,
                  [fn],
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_22<T>(
              this,
              Invocation.method(
                #doWhenOpened,
                [fn],
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i3.InsertStatement<T, D> into<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #into,
          [table],
        ),
        returnValue: _FakeInsertStatement_23<T, D>(
          this,
          Invocation.method(
            #into,
            [table],
          ),
        ),
      ) as _i3.InsertStatement<T, D>);

  @override
  _i3.UpdateStatement<Tbl, R> update<Tbl extends _i3.Table, R>(
          _i3.TableInfo<Tbl, R>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [table],
        ),
        returnValue: _FakeUpdateStatement_24<Tbl, R>(
          this,
          Invocation.method(
            #update,
            [table],
          ),
        ),
      ) as _i3.UpdateStatement<Tbl, R>);

  @override
  _i3.SimpleSelectStatement<T, R> select<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #select,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeSimpleSelectStatement_25<T, R>(
          this,
          Invocation.method(
            #select,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.SimpleSelectStatement<T, R>);

  @override
  _i3.JoinedSelectStatement<T, R> selectOnly<T extends _i3.HasResultSet, R>(
    _i3.ResultSetImplementation<T, R>? table, {
    bool? distinct = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectOnly,
          [table],
          {#distinct: distinct},
        ),
        returnValue: _FakeJoinedSelectStatement_26<T, R>(
          this,
          Invocation.method(
            #selectOnly,
            [table],
            {#distinct: distinct},
          ),
        ),
      ) as _i3.JoinedSelectStatement<T, R>);

  @override
  _i3.BaseSelectStatement<_i3.TypedResult> selectExpressions(
          Iterable<_i3.Expression<Object>>? columns) =>
      (super.noSuchMethod(
        Invocation.method(
          #selectExpressions,
          [columns],
        ),
        returnValue: _FakeBaseSelectStatement_27<_i3.TypedResult>(
          this,
          Invocation.method(
            #selectExpressions,
            [columns],
          ),
        ),
      ) as _i3.BaseSelectStatement<_i3.TypedResult>);

  @override
  _i3.DeleteStatement<T, D> delete<T extends _i3.Table, D>(
          _i3.TableInfo<T, D>? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [table],
        ),
        returnValue: _FakeDeleteStatement_28<T, D>(
          this,
          Invocation.method(
            #delete,
            [table],
          ),
        ),
      ) as _i3.DeleteStatement<T, D>);

  @override
  _i6.Future<int> customUpdate(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customUpdate,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<int> customInsert(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customInsert,
          [query],
          {
            #variables: variables,
            #updates: updates,
          },
        ),
        returnValue: _i6.Future<int>.value(0),
      ) as _i6.Future<int>);

  @override
  _i6.Future<List<_i3.QueryRow>> customWriteReturning(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? updates,
    _i3.UpdateKind? updateKind,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customWriteReturning,
          [query],
          {
            #variables: variables,
            #updates: updates,
            #updateKind: updateKind,
          },
        ),
        returnValue: _i6.Future<List<_i3.QueryRow>>.value(<_i3.QueryRow>[]),
      ) as _i6.Future<List<_i3.QueryRow>>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelect(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelect,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_29<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelect,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i3.Selectable<_i3.QueryRow> customSelectQuery(
    String? query, {
    List<_i3.Variable<Object>>? variables = const [],
    Set<_i3.ResultSetImplementation<dynamic, dynamic>>? readsFrom = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #customSelectQuery,
          [query],
          {
            #variables: variables,
            #readsFrom: readsFrom,
          },
        ),
        returnValue: _FakeSelectable_29<_i3.QueryRow>(
          this,
          Invocation.method(
            #customSelectQuery,
            [query],
            {
              #variables: variables,
              #readsFrom: readsFrom,
            },
          ),
        ),
      ) as _i3.Selectable<_i3.QueryRow>);

  @override
  _i6.Future<void> customStatement(
    String? statement, [
    List<dynamic>? args,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #customStatement,
          [
            statement,
            args,
          ],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<T> transaction<T>(
    _i6.Future<T> Function()? action, {
    bool? requireNew = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #transaction,
          [action],
          {#requireNew: requireNew},
        ),
        returnValue: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #transaction,
                  [action],
                  {#requireNew: requireNew},
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_22<T>(
              this,
              Invocation.method(
                #transaction,
                [action],
                {#requireNew: requireNew},
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i6.Future<T> exclusively<T>(_i6.Future<T> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #exclusively,
          [action],
        ),
        returnValue: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #exclusively,
                  [action],
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_22<T>(
              this,
              Invocation.method(
                #exclusively,
                [action],
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i6.Future<void> batch(_i6.FutureOr<void> Function(_i3.Batch)? runInBatch) =>
      (super.noSuchMethod(
        Invocation.method(
          #batch,
          [runInBatch],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<T> runWithInterceptor<T>(
    _i6.Future<T> Function()? action, {
    required _i3.QueryInterceptor? interceptor,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runWithInterceptor,
          [action],
          {#interceptor: interceptor},
        ),
        returnValue: _i7.ifNotNull(
              _i7.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runWithInterceptor,
                  [action],
                  {#interceptor: interceptor},
                ),
              ),
              (T v) => _i6.Future<T>.value(v),
            ) ??
            _FakeFuture_22<T>(
              this,
              Invocation.method(
                #runWithInterceptor,
                [action],
                {#interceptor: interceptor},
              ),
            ),
      ) as _i6.Future<T>);

  @override
  _i3.GenerationContext $write(
    _i3.Component? component, {
    bool? hasMultipleTables,
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$write,
          [component],
          {
            #hasMultipleTables: hasMultipleTables,
            #startIndex: startIndex,
          },
        ),
        returnValue: _FakeGenerationContext_30(
          this,
          Invocation.method(
            #$write,
            [component],
            {
              #hasMultipleTables: hasMultipleTables,
              #startIndex: startIndex,
            },
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  _i3.GenerationContext $writeInsertable(
    _i3.TableInfo<_i3.Table, dynamic>? table,
    _i3.Insertable<dynamic>? insertable, {
    int? startIndex,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #$writeInsertable,
          [
            table,
            insertable,
          ],
          {#startIndex: startIndex},
        ),
        returnValue: _FakeGenerationContext_30(
          this,
          Invocation.method(
            #$writeInsertable,
            [
              table,
              insertable,
            ],
            {#startIndex: startIndex},
          ),
        ),
      ) as _i3.GenerationContext);

  @override
  String $expandVar(
    int? start,
    int? amount,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #$expandVar,
          [
            start,
            amount,
          ],
        ),
        returnValue: _i7.dummyValue<String>(
          this,
          Invocation.method(
            #$expandVar,
            [
              start,
              amount,
            ],
          ),
        ),
      ) as String);
}
