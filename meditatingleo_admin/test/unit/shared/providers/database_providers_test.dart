import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/shared/providers/database_providers.dart';
import 'package:meditatingleo_admin/core/services/supabase_service.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';

// Generate mocks
@GenerateMocks([SupabaseClient, AdminDatabase])
import 'database_providers_test.mocks.dart';

void main() {
  group('Database Providers', () {
    late ProviderContainer container;
    late MockSupabaseClient mockSupabaseClient;
    late MockAdminDatabase mockDatabase;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockDatabase = MockAdminDatabase();

      container = ProviderContainer(
        overrides: [
          // Override providers with mocks for testing
          supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          adminDatabaseProvider.overrideWithValue(mockDatabase),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('supabaseClientProvider', () {
      test('should provide SupabaseClient instance', () {
        // Act
        final client = container.read(supabaseClientProvider);

        // Assert
        expect(client, isNotNull);
        expect(client, isA<SupabaseClient>());
      });
    });

    group('adminDatabaseProvider', () {
      test('should provide AdminDatabase instance', () {
        // Act
        final database = container.read(adminDatabaseProvider);

        // Assert
        expect(database, isNotNull);
        expect(database, isA<AdminDatabase>());
      });
    });

    group('supabaseServiceProvider', () {
      test('should provide SupabaseService instance with dependencies', () {
        // Act
        final service = container.read(supabaseServiceProvider);

        // Assert
        expect(service, isNotNull);
        expect(service, isA<SupabaseService>());
      });

      test('should use the same SupabaseClient instance', () {
        // Act
        final service1 = container.read(supabaseServiceProvider);
        final service2 = container.read(supabaseServiceProvider);

        // Assert
        expect(service1,
            equals(service2)); // Should be the same instance due to caching
      });
    });

    group('Provider Dependencies', () {
      test('should maintain proper dependency injection', () {
        // Arrange & Act
        final supabaseClient = container.read(supabaseClientProvider);
        final database = container.read(adminDatabaseProvider);
        final service = container.read(supabaseServiceProvider);

        // Assert
        expect(supabaseClient, isNotNull);
        expect(database, isNotNull);
        expect(service, isNotNull);

        // Verify that the service uses the injected client
        expect(service, isA<SupabaseService>());
      });

      test('should handle provider disposal correctly', () {
        // Arrange
        final service = container.read(supabaseServiceProvider);
        expect(service, isNotNull);

        // Act
        container.dispose();

        // Assert - No exceptions should be thrown during disposal
        expect(() => container.dispose(), returnsNormally);
      });
    });

    group('Provider Overrides', () {
      test('should allow provider overrides for testing', () {
        // Arrange
        final testContainer = ProviderContainer(
          overrides: [
            supabaseServiceProvider.overrideWith((ref) {
              return SupabaseService(mockSupabaseClient);
            }),
          ],
        );

        // Act
        final service = testContainer.read(supabaseServiceProvider);

        // Assert
        expect(service, isNotNull);
        expect(service, isA<SupabaseService>());

        // Cleanup
        testContainer.dispose();
      });

      test('should maintain override isolation between containers', () {
        // Arrange
        final container1 = ProviderContainer(
          overrides: [
            supabaseClientProvider.overrideWithValue(mockSupabaseClient),
          ],
        );

        final container2 = ProviderContainer(
          overrides: [
            supabaseClientProvider.overrideWithValue(MockSupabaseClient()),
          ],
        ); // Different overrides

        // Act
        final client1 = container1.read(supabaseClientProvider);
        final client2 = container2.read(supabaseClientProvider);

        // Assert
        expect(client1, equals(mockSupabaseClient));
        expect(client2, isNot(equals(mockSupabaseClient)));

        // Cleanup
        container1.dispose();
        container2.dispose();
      });
    });

    group('Provider Lifecycle', () {
      test('should initialize providers lazily', () {
        // Arrange
        final freshContainer = ProviderContainer(
          overrides: [
            supabaseClientProvider.overrideWithValue(MockSupabaseClient()),
          ],
        );

        // Act & Assert - Providers should not be initialized until accessed
        expect(() => freshContainer.read(supabaseServiceProvider),
            returnsNormally);

        // Cleanup
        freshContainer.dispose();
      });

      test('should cache provider instances', () {
        // Act
        final service1 = container.read(supabaseServiceProvider);
        final service2 = container.read(supabaseServiceProvider);
        final database1 = container.read(adminDatabaseProvider);
        final database2 = container.read(adminDatabaseProvider);

        // Assert
        expect(identical(service1, service2), isTrue);
        expect(identical(database1, database2), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle provider creation errors gracefully', () {
        // Arrange
        final errorContainer = ProviderContainer(
          overrides: [
            supabaseClientProvider.overrideWith((ref) {
              throw Exception('Mock initialization error');
            }),
          ],
        );

        // Act & Assert
        expect(
          () => errorContainer.read(supabaseServiceProvider),
          throwsException,
        );

        // Cleanup
        errorContainer.dispose();
      });
    });
  });
}
