import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:meditatingleo_admin/features/authentication/presentation/providers/auth_providers.dart';
import 'package:meditatingleo_admin/core/services/supabase_service.dart';
import 'package:meditatingleo_admin/shared/providers/database_providers.dart';
import 'package:meditatingleo_admin/shared/models/result.dart';

import 'auth_providers_test.mocks.dart';

@GenerateMocks([SupabaseService, User, AuthResponse, Session])
void main() {
  group('AuthProviders', () {
    late ProviderContainer container;
    late MockSupabaseService mockSupabaseService;
    late MockUser mockUser;
    late MockAuthResponse mockAuthResponse;
    late MockSession mockSession;

    setUp(() {
      mockSupabaseService = MockSupabaseService();
      mockUser = MockUser();
      mockAuthResponse = MockAuthResponse();
      mockSession = MockSession();

      // Set up default stubs
      when(mockSupabaseService.getCurrentUser()).thenReturn(null);
      when(mockSupabaseService.getUserRole()).thenReturn(null);
      when(mockSupabaseService.getUserPermissions()).thenReturn([]);
      when(mockSupabaseService.hasAdminPermissions()).thenReturn(false);
      when(mockSupabaseService.signOut())
          .thenAnswer((_) async => Result.success(null));

      container = ProviderContainer(
        overrides: [
          supabaseServiceProvider.overrideWithValue(mockSupabaseService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('AdminAuthNotifier', () {
      test('should initialize with unauthenticated state', () {
        // Act
        final state = container.read(adminAuthNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<AdminAuthState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.user, isNull);
        expect(state.value?.isAuthenticated, isFalse);
        expect(state.value?.isLoading, isFalse);
      });

      test('should sign in successfully with valid credentials', () async {
        // Arrange
        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockSession.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);
        when(mockAuthResponse.user).thenReturn(mockUser);

        when(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'password123',
        )).thenAnswer((_) async => Result.success(mockAuthResponse));

        when(mockSupabaseService.hasAdminPermissions()).thenReturn(true);
        when(mockSupabaseService.getUserRole()).thenReturn('admin');
        when(mockSupabaseService.getUserPermissions())
            .thenReturn(['read', 'write']);

        // Act
        await container
            .read(adminAuthNotifierProvider.notifier)
            .signIn('<EMAIL>', 'password123');

        // Assert
        final state = container.read(adminAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isAuthenticated, isTrue);
        expect(state.value?.user, isNotNull);
        verify(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'password123',
        )).called(1);
      });

      test('should handle sign in error', () async {
        // Arrange
        when(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'wrongpassword',
        )).thenThrow(AuthException('Invalid credentials'));

        // Act
        await container
            .read(adminAuthNotifierProvider.notifier)
            .signIn('<EMAIL>', 'wrongpassword');

        // Assert
        final state = container.read(adminAuthNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Invalid credentials'));
      });

      test('should reject non-admin users', () async {
        // Arrange
        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockSession.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);
        when(mockAuthResponse.user).thenReturn(mockUser);

        when(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'password123',
        )).thenAnswer((_) async => Result.success(mockAuthResponse));

        when(mockSupabaseService.hasAdminPermissions()).thenReturn(false);
        when(mockSupabaseService.signOut())
            .thenAnswer((_) async => Result.success(null));

        // Act
        await container
            .read(adminAuthNotifierProvider.notifier)
            .signIn('<EMAIL>', 'password123');

        // Assert
        final state = container.read(adminAuthNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('admin permissions'));
      });

      test('should sign out successfully', () async {
        // Arrange
        when(mockSupabaseService.signOut())
            .thenAnswer((_) async => Result.success(null));

        // Act
        await container.read(adminAuthNotifierProvider.notifier).signOut();

        // Assert
        final state = container.read(adminAuthNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isAuthenticated, isFalse);
        expect(state.value?.user, isNull);
        verify(mockSupabaseService.signOut()).called(1);
      });

      test('should handle sign out error', () async {
        // Arrange
        when(mockSupabaseService.signOut())
            .thenThrow(Exception('Sign out failed'));

        // Act
        await container.read(adminAuthNotifierProvider.notifier).signOut();

        // Assert
        final state = container.read(adminAuthNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Sign out failed'));
      });
    });

    group('LoginFormNotifier', () {
      test('should initialize with empty form state', () {
        // Act
        final state = container.read(loginFormNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<LoginFormState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.email, isEmpty);
        expect(state.value?.password, isEmpty);
        expect(state.value?.isLoading, isFalse);
        expect(state.value?.isValid, isFalse);
      });

      test('should update email and validate', () async {
        // Act
        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('<EMAIL>');

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.email, equals('<EMAIL>'));
        expect(state.value?.emailError, isNull);
      });

      test('should validate invalid email', () async {
        // Act
        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('invalid-email');

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.email, equals('invalid-email'));
        expect(state.value?.emailError, isNotNull);
        expect(state.value?.isValid, isFalse);
      });

      test('should update password and validate', () async {
        // Act
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('password123');

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.password, equals('password123'));
        expect(state.value?.passwordError, isNull);
      });

      test('should validate short password', () async {
        // Act
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('123');

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.password, equals('123'));
        expect(state.value?.passwordError, isNotNull);
        expect(state.value?.isValid, isFalse);
      });

      test('should validate complete form', () async {
        // Act
        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('<EMAIL>');
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('password123');

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isValid, isTrue);
        expect(state.value?.emailError, isNull);
        expect(state.value?.passwordError, isNull);
      });

      test('should submit form successfully', () async {
        // Arrange
        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockSession.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);
        when(mockAuthResponse.user).thenReturn(mockUser);

        when(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'password123',
        )).thenAnswer((_) async => Result.success(mockAuthResponse));

        when(mockSupabaseService.hasAdminPermissions()).thenReturn(true);
        when(mockSupabaseService.getUserRole()).thenReturn('admin');
        when(mockSupabaseService.getUserPermissions())
            .thenReturn(['read', 'write']);

        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('<EMAIL>');
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('password123');

        // Act
        await container.read(loginFormNotifierProvider.notifier).submit();

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isLoading, isFalse);
        expect(state.value?.errorMessage, isNull);
      });

      test('should handle form submission error', () async {
        // Arrange
        when(mockSupabaseService.signInWithEmail(
          '<EMAIL>',
          'wrongpass123',
        )).thenThrow(AuthException('Invalid credentials'));

        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('<EMAIL>');
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('wrongpass123');

        // Act
        await container.read(loginFormNotifierProvider.notifier).submit();

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isLoading, isFalse);
        expect(state.value?.errorMessage, isNotNull);
        expect(state.value?.errorMessage, contains('Invalid credentials'));
      });

      test('should clear form', () async {
        // Arrange
        await container
            .read(loginFormNotifierProvider.notifier)
            .updateEmail('<EMAIL>');
        await container
            .read(loginFormNotifierProvider.notifier)
            .updatePassword('password123');

        // Act
        await container.read(loginFormNotifierProvider.notifier).clear();

        // Assert
        final state = container.read(loginFormNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.email, isEmpty);
        expect(state.value?.password, isEmpty);
        expect(state.value?.isValid, isFalse);
        expect(state.value?.errorMessage, isNull);
      });
    });
  });
}
