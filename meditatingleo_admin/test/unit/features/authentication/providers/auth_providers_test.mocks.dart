// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/features/authentication/providers/auth_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:meditatingleo_admin/core/services/supabase_service.dart' as _i4;
import 'package:meditatingleo_admin/shared/models/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:supabase_flutter/supabase_flutter.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T, E> extends _i1.SmartFake implements _i2.Result<T, E> {
  _FakeResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUser_1 extends _i1.SmartFake implements _i3.User {
  _FakeUser_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSession_2 extends _i1.SmartFake implements _i3.Session {
  _FakeSession_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseService extends _i1.Mock implements _i4.SupabaseService {
  MockSupabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i3.AuthState> get authStateStream => (super.noSuchMethod(
        Invocation.getter(#authStateStream),
        returnValue: _i5.Stream<_i3.AuthState>.empty(),
      ) as _i5.Stream<_i3.AuthState>);

  @override
  _i5.Future<_i2.Result<_i3.AuthResponse, _i2.AppError>> signInWithEmail(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmail,
          [
            email,
            password,
          ],
        ),
        returnValue:
            _i5.Future<_i2.Result<_i3.AuthResponse, _i2.AppError>>.value(
                _FakeResult_0<_i3.AuthResponse, _i2.AppError>(
          this,
          Invocation.method(
            #signInWithEmail,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<_i3.AuthResponse, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<void, _i2.AppError>> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i5.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #signOut,
            [],
          ),
        )),
      ) as _i5.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> query(
          String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [tableName],
        ),
        returnValue: _i5
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #query,
            [tableName],
          ),
        )),
      ) as _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> queryById(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryById,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i5
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #queryById,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> insert(
    String? tableName,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            tableName,
            data,
          ],
        ),
        returnValue: _i5
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #insert,
            [
              tableName,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>> update(
    String? tableName,
    String? id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            tableName,
            id,
            data,
          ],
        ),
        returnValue: _i5
            .Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>.value(
            _FakeResult_0<List<Map<String, dynamic>>, _i2.AppError>(
          this,
          Invocation.method(
            #update,
            [
              tableName,
              id,
              data,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<List<Map<String, dynamic>>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<void, _i2.AppError>> delete(
    String? tableName,
    String? id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [
            tableName,
            id,
          ],
        ),
        returnValue: _i5.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #delete,
            [
              tableName,
              id,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<dynamic, _i2.AppError>> rpc(
    String? functionName,
    Map<String, dynamic>? params,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [
            functionName,
            params,
          ],
        ),
        returnValue: _i5.Future<_i2.Result<dynamic, _i2.AppError>>.value(
            _FakeResult_0<dynamic, _i2.AppError>(
          this,
          Invocation.method(
            #rpc,
            [
              functionName,
              params,
            ],
          ),
        )),
      ) as _i5.Future<_i2.Result<dynamic, _i2.AppError>>);

  @override
  _i5.Stream<Map<String, dynamic>> subscribe(String? tableName) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribe,
          [tableName],
        ),
        returnValue: _i5.Stream<Map<String, dynamic>>.empty(),
      ) as _i5.Stream<Map<String, dynamic>>);

  @override
  bool hasAdminPermissions() => (super.noSuchMethod(
        Invocation.method(
          #hasAdminPermissions,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  List<String> getUserPermissions() => (super.noSuchMethod(
        Invocation.method(
          #getUserPermissions,
          [],
        ),
        returnValue: <String>[],
      ) as List<String>);
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i3.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  Map<String, dynamic> get appMetadata => (super.noSuchMethod(
        Invocation.getter(#appMetadata),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  String get aud => (super.noSuchMethod(
        Invocation.getter(#aud),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#aud),
        ),
      ) as String);

  @override
  String get createdAt => (super.noSuchMethod(
        Invocation.getter(#createdAt),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#createdAt),
        ),
      ) as String);

  @override
  bool get isAnonymous => (super.noSuchMethod(
        Invocation.getter(#isAnonymous),
        returnValue: false,
      ) as bool);

  @override
  Map<String, dynamic> toJson() => (super.noSuchMethod(
        Invocation.method(
          #toJson,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}

/// A class which mocks [AuthResponse].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthResponse extends _i1.Mock implements _i3.AuthResponse {
  MockAuthResponse() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [Session].
///
/// See the documentation for Mockito's code generation for more information.
class MockSession extends _i1.Mock implements _i3.Session {
  MockSession() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get accessToken => (super.noSuchMethod(
        Invocation.getter(#accessToken),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#accessToken),
        ),
      ) as String);

  @override
  String get tokenType => (super.noSuchMethod(
        Invocation.getter(#tokenType),
        returnValue: _i6.dummyValue<String>(
          this,
          Invocation.getter(#tokenType),
        ),
      ) as String);

  @override
  _i3.User get user => (super.noSuchMethod(
        Invocation.getter(#user),
        returnValue: _FakeUser_1(
          this,
          Invocation.getter(#user),
        ),
      ) as _i3.User);

  @override
  bool get isExpired => (super.noSuchMethod(
        Invocation.getter(#isExpired),
        returnValue: false,
      ) as bool);

  @override
  set expiresAt(int? _expiresAt) => super.noSuchMethod(
        Invocation.setter(
          #expiresAt,
          _expiresAt,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> toJson() => (super.noSuchMethod(
        Invocation.method(
          #toJson,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i3.Session copyWith({
    String? accessToken,
    int? expiresIn,
    String? refreshToken,
    String? tokenType,
    String? providerToken,
    String? providerRefreshToken,
    _i3.User? user,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #copyWith,
          [],
          {
            #accessToken: accessToken,
            #expiresIn: expiresIn,
            #refreshToken: refreshToken,
            #tokenType: tokenType,
            #providerToken: providerToken,
            #providerRefreshToken: providerRefreshToken,
            #user: user,
          },
        ),
        returnValue: _FakeSession_2(
          this,
          Invocation.method(
            #copyWith,
            [],
            {
              #accessToken: accessToken,
              #expiresIn: expiresIn,
              #refreshToken: refreshToken,
              #tokenType: tokenType,
              #providerToken: providerToken,
              #providerRefreshToken: providerRefreshToken,
              #user: user,
            },
          ),
        ),
      ) as _i3.Session);
}
