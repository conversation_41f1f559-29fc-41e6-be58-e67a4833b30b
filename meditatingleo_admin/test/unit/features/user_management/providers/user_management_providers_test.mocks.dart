// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/features/user_management/providers/user_management_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:meditatingleo_admin/core/repositories/user_repository.dart'
    as _i4;
import 'package:meditatingleo_admin/features/user_management/domain/entities/admin_user_entity.dart'
    as _i2;
import 'package:meditatingleo_admin/features/user_management/domain/entities/user_analytics_entity.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAdminUserEntity_0 extends _i1.SmartFake
    implements _i2.AdminUserEntity {
  _FakeAdminUserEntity_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserAnalyticsEntity_1 extends _i1.SmartFake
    implements _i3.UserAnalyticsEntity {
  _FakeUserAnalyticsEntity_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i4.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i2.AdminUserEntity>> getAllUsers() => (super.noSuchMethod(
        Invocation.method(
          #getAllUsers,
          [],
        ),
        returnValue: _i5.Future<List<_i2.AdminUserEntity>>.value(
            <_i2.AdminUserEntity>[]),
      ) as _i5.Future<List<_i2.AdminUserEntity>>);

  @override
  _i5.Future<_i2.AdminUserEntity?> getUserById(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserById,
          [userId],
        ),
        returnValue: _i5.Future<_i2.AdminUserEntity?>.value(),
      ) as _i5.Future<_i2.AdminUserEntity?>);

  @override
  _i5.Future<_i2.AdminUserEntity> updateUserStatus(
    String? userId,
    bool? isActive,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserStatus,
          [
            userId,
            isActive,
          ],
        ),
        returnValue:
            _i5.Future<_i2.AdminUserEntity>.value(_FakeAdminUserEntity_0(
          this,
          Invocation.method(
            #updateUserStatus,
            [
              userId,
              isActive,
            ],
          ),
        )),
      ) as _i5.Future<_i2.AdminUserEntity>);

  @override
  _i5.Future<_i2.AdminUserEntity> updateUserRole(
    String? userId,
    String? role,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateUserRole,
          [
            userId,
            role,
          ],
        ),
        returnValue:
            _i5.Future<_i2.AdminUserEntity>.value(_FakeAdminUserEntity_0(
          this,
          Invocation.method(
            #updateUserRole,
            [
              userId,
              role,
            ],
          ),
        )),
      ) as _i5.Future<_i2.AdminUserEntity>);

  @override
  _i5.Future<void> deleteUser(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #deleteUser,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> bulkUpdateUserStatus(
    List<String>? userIds,
    bool? isActive,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkUpdateUserStatus,
          [
            userIds,
            isActive,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> bulkUpdateUserRole(
    List<String>? userIds,
    String? role,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkUpdateUserRole,
          [
            userIds,
            role,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> bulkDeleteUsers(List<String>? userIds) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeleteUsers,
          [userIds],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i3.UserAnalyticsEntity> getUserAnalytics() => (super.noSuchMethod(
        Invocation.method(
          #getUserAnalytics,
          [],
        ),
        returnValue: _i5.Future<_i3.UserAnalyticsEntity>.value(
            _FakeUserAnalyticsEntity_1(
          this,
          Invocation.method(
            #getUserAnalytics,
            [],
          ),
        )),
      ) as _i5.Future<_i3.UserAnalyticsEntity>);

  @override
  _i5.Future<List<_i2.AdminUserEntity>> searchUsers(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchUsers,
          [query],
        ),
        returnValue: _i5.Future<List<_i2.AdminUserEntity>>.value(
            <_i2.AdminUserEntity>[]),
      ) as _i5.Future<List<_i2.AdminUserEntity>>);

  @override
  _i5.Future<List<_i2.AdminUserEntity>> getUsersByRole(String? role) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsersByRole,
          [role],
        ),
        returnValue: _i5.Future<List<_i2.AdminUserEntity>>.value(
            <_i2.AdminUserEntity>[]),
      ) as _i5.Future<List<_i2.AdminUserEntity>>);

  @override
  _i5.Future<List<_i2.AdminUserEntity>> getUsersByStatus(bool? isActive) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsersByStatus,
          [isActive],
        ),
        returnValue: _i5.Future<List<_i2.AdminUserEntity>>.value(
            <_i2.AdminUserEntity>[]),
      ) as _i5.Future<List<_i2.AdminUserEntity>>);

  @override
  _i5.Future<List<_i2.AdminUserEntity>> getUsersByDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUsersByDateRange,
          [
            startDate,
            endDate,
          ],
        ),
        returnValue: _i5.Future<List<_i2.AdminUserEntity>>.value(
            <_i2.AdminUserEntity>[]),
      ) as _i5.Future<List<_i2.AdminUserEntity>>);
}
