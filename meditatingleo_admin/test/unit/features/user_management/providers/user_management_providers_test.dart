import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:meditatingleo_admin/features/user_management/presentation/providers/user_management_providers.dart';
import 'package:meditatingleo_admin/features/user_management/domain/entities/admin_user_entity.dart';
import 'package:meditatingleo_admin/features/user_management/domain/entities/user_analytics_entity.dart';
import 'package:meditatingleo_admin/core/repositories/user_repository.dart';

import 'user_management_providers_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  group('UserManagementProviders', () {
    late ProviderContainer container;
    late MockUserRepository mockUserRepository;

    setUp(() {
      mockUserRepository = MockUserRepository();
      container = ProviderContainer(
        overrides: [
          userRepositoryProvider.overrideWithValue(mockUserRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('UserManagementNotifier', () {
      test('should initialize with empty state', () {
        // Act
        final state = container.read(userManagementNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<UserManagementState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.users, isEmpty);
        expect(state.value?.selectedUser, isNull);
        expect(state.value?.isLoading, isFalse);
      });

      test('should load users successfully', () async {
        // Arrange
        final mockUsers = [
          AdminUserEntity(
            id: '1',
            email: '<EMAIL>',
            name: 'User One',
            role: 'user',
            isActive: true,
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          ),
          AdminUserEntity(
            id: '2',
            email: '<EMAIL>',
            name: 'User Two',
            role: 'user',
            isActive: true,
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          ),
        ];
        when(mockUserRepository.getAllUsers())
            .thenAnswer((_) async => mockUsers);

        // Act
        await container
            .read(userManagementNotifierProvider.notifier)
            .loadUsers();

        // Assert
        final state = container.read(userManagementNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.users, equals(mockUsers));
        verify(mockUserRepository.getAllUsers()).called(1);
      });

      test('should handle user loading error', () async {
        // Arrange
        when(mockUserRepository.getAllUsers())
            .thenThrow(Exception('Failed to load users'));

        // Act
        await container
            .read(userManagementNotifierProvider.notifier)
            .loadUsers();

        // Assert
        final state = container.read(userManagementNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Failed to load users'));
      });

      test('should select user', () async {
        // Arrange
        final user = AdminUserEntity(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
          isActive: true,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        // Act
        await container
            .read(userManagementNotifierProvider.notifier)
            .selectUser(user);

        // Assert
        final state = container.read(userManagementNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.selectedUser, equals(user));
      });

      test('should update user status', () async {
        // Arrange
        final user = AdminUserEntity(
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'user',
          isActive: true,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );
        when(mockUserRepository.updateUserStatus('1', false))
            .thenAnswer((_) async => user.copyWith(isActive: false));
        when(mockUserRepository.getAllUsers())
            .thenAnswer((_) async => [user.copyWith(isActive: false)]);

        // Act
        await container
            .read(userManagementNotifierProvider.notifier)
            .updateUserStatus('1', false);

        // Assert
        verify(mockUserRepository.updateUserStatus('1', false)).called(1);
        verify(mockUserRepository.getAllUsers()).called(1);
      });

      test('should delete user', () async {
        // Arrange
        when(mockUserRepository.deleteUser('1'))
            .thenAnswer((_) async => {});
        when(mockUserRepository.getAllUsers())
            .thenAnswer((_) async => []);

        // Act
        await container
            .read(userManagementNotifierProvider.notifier)
            .deleteUser('1');

        // Assert
        verify(mockUserRepository.deleteUser('1')).called(1);
        verify(mockUserRepository.getAllUsers()).called(1);
      });
    });

    group('UserAnalyticsNotifier', () {
      test('should initialize with empty analytics state', () {
        // Act
        final state = container.read(userAnalyticsNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<UserAnalyticsState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.totalUsers, equals(0));
        expect(state.value?.activeUsers, equals(0));
        expect(state.value?.newUsersThisMonth, equals(0));
      });

      test('should load analytics successfully', () async {
        // Arrange
        final mockAnalytics = UserAnalyticsEntity(
          totalUsers: 100,
          activeUsers: 85,
          newUsersThisMonth: 15,
          averageSessionDuration: 25.5,
          topJourneys: ['Journey 1', 'Journey 2'],
          userGrowthData: [10, 15, 20, 25, 30],
          engagementMetrics: {'daily_active': 60, 'weekly_active': 80},
          generatedAt: DateTime.now(),
        );
        when(mockUserRepository.getUserAnalytics())
            .thenAnswer((_) async => mockAnalytics);

        // Act
        await container
            .read(userAnalyticsNotifierProvider.notifier)
            .loadAnalytics();

        // Assert
        final state = container.read(userAnalyticsNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.totalUsers, equals(100));
        expect(state.value?.activeUsers, equals(85));
        expect(state.value?.newUsersThisMonth, equals(15));
        verify(mockUserRepository.getUserAnalytics()).called(1);
      });

      test('should handle analytics loading error', () async {
        // Arrange
        when(mockUserRepository.getUserAnalytics())
            .thenThrow(Exception('Failed to load analytics'));

        // Act
        await container
            .read(userAnalyticsNotifierProvider.notifier)
            .loadAnalytics();

        // Assert
        final state = container.read(userAnalyticsNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Failed to load analytics'));
      });

      test('should refresh analytics', () async {
        // Arrange
        final mockAnalytics = UserAnalyticsEntity(
          totalUsers: 105,
          activeUsers: 90,
          newUsersThisMonth: 20,
          averageSessionDuration: 28.0,
          topJourneys: ['Journey 1', 'Journey 3'],
          userGrowthData: [12, 18, 22, 28, 35],
          engagementMetrics: {'daily_active': 65, 'weekly_active': 85},
          generatedAt: DateTime.now(),
        );
        when(mockUserRepository.getUserAnalytics())
            .thenAnswer((_) async => mockAnalytics);

        // Act
        await container
            .read(userAnalyticsNotifierProvider.notifier)
            .refreshAnalytics();

        // Assert
        final state = container.read(userAnalyticsNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.totalUsers, equals(105));
        verify(mockUserRepository.getUserAnalytics()).called(1);
      });
    });

    group('BulkOperationsNotifier', () {
      test('should initialize with empty bulk operations state', () {
        // Act
        final state = container.read(bulkOperationsNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<BulkOperationsState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.selectedUserIds, isEmpty);
        expect(state.value?.isProcessing, isFalse);
      });

      test('should select users for bulk operations', () async {
        // Arrange
        final userIds = ['1', '2', '3'];

        // Act
        await container
            .read(bulkOperationsNotifierProvider.notifier)
            .selectUsers(userIds);

        // Assert
        final state = container.read(bulkOperationsNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.selectedUserIds, equals(userIds));
      });

      test('should perform bulk status update', () async {
        // Arrange
        final userIds = ['1', '2', '3'];
        when(mockUserRepository.bulkUpdateUserStatus(userIds, false))
            .thenAnswer((_) async => {});

        await container
            .read(bulkOperationsNotifierProvider.notifier)
            .selectUsers(userIds);

        // Act
        await container
            .read(bulkOperationsNotifierProvider.notifier)
            .bulkUpdateStatus(false);

        // Assert
        verify(mockUserRepository.bulkUpdateUserStatus(userIds, false)).called(1);
        final state = container.read(bulkOperationsNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isProcessing, isFalse);
      });

      test('should clear selection', () async {
        // Arrange
        await container
            .read(bulkOperationsNotifierProvider.notifier)
            .selectUsers(['1', '2', '3']);

        // Act
        await container
            .read(bulkOperationsNotifierProvider.notifier)
            .clearSelection();

        // Assert
        final state = container.read(bulkOperationsNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.selectedUserIds, isEmpty);
      });
    });
  });
}
