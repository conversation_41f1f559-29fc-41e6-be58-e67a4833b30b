import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:meditatingleo_admin/features/content_management/presentation/providers/content_management_providers.dart';
import 'package:meditatingleo_admin/features/content_management/domain/entities/journey_entity.dart';
import 'package:meditatingleo_admin/features/content_management/domain/entities/prompt_entity.dart';
import 'package:meditatingleo_admin/core/repositories/content_repository.dart';
import 'package:meditatingleo_admin/shared/providers/repository_providers.dart';

import 'content_management_providers_test.mocks.dart';

@GenerateMocks([ContentRepository])
void main() {
  group('ContentManagementProviders', () {
    late ProviderContainer container;
    late MockContentRepository mockContentRepository;

    setUp(() {
      mockContentRepository = MockContentRepository();
      container = ProviderContainer(
        overrides: [
          contentRepositoryProvider.overrideWithValue(mockContentRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('ContentManagementNotifier', () {
      test('should initialize with empty state', () {
        // Act
        final state = container.read(contentManagementNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<ContentManagementState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.journeys, isEmpty);
        expect(state.value?.selectedJourney, isNull);
        expect(state.value?.isLoading, isFalse);
      });

      test('should load journeys successfully', () async {
        // Arrange
        final mockJourneys = [
          JourneyEntity(
            id: '1',
            title: 'Test Journey',
            description: 'Test Description',
            prompts: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        when(mockContentRepository.getAllJourneys())
            .thenAnswer((_) async => mockJourneys);

        // Act
        await container
            .read(contentManagementNotifierProvider.notifier)
            .loadJourneys();

        // Assert
        final state = container.read(contentManagementNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.journeys, equals(mockJourneys));
        verify(mockContentRepository.getAllJourneys()).called(1);
      });

      test('should handle journey loading error', () async {
        // Arrange
        when(mockContentRepository.getAllJourneys())
            .thenThrow(Exception('Failed to load journeys'));

        // Act
        await container
            .read(contentManagementNotifierProvider.notifier)
            .loadJourneys();

        // Assert
        final state = container.read(contentManagementNotifierProvider);
        expect(state.hasError, isTrue);
        expect(state.error.toString(), contains('Failed to load journeys'));
      });

      test('should create new journey successfully', () async {
        // Arrange
        final newJourney = JourneyEntity(
          id: '2',
          title: 'New Journey',
          description: 'New Description',
          prompts: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockContentRepository.createJourney(argThat(isA<JourneyEntity>())))
            .thenAnswer((_) async => newJourney);
        when(mockContentRepository.getAllJourneys())
            .thenAnswer((_) async => [newJourney]);

        // Act
        await container
            .read(contentManagementNotifierProvider.notifier)
            .createJourney('New Journey', 'New Description');

        // Assert
        final state = container.read(contentManagementNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.journeys, contains(newJourney));
        verify(mockContentRepository
                .createJourney(argThat(isA<JourneyEntity>())))
            .called(1);
      });

      test('should select journey', () async {
        // Arrange
        final journey = JourneyEntity(
          id: '1',
          title: 'Test Journey',
          description: 'Test Description',
          prompts: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        await container
            .read(contentManagementNotifierProvider.notifier)
            .selectJourney(journey);

        // Assert
        final state = container.read(contentManagementNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.selectedJourney, equals(journey));
      });
    });

    group('JourneyBuilderNotifier', () {
      test('should initialize with empty journey builder state', () {
        // Act
        final state = container.read(journeyBuilderNotifierProvider);

        // Assert
        expect(state, isA<AsyncValue<JourneyBuilderState>>());
        expect(state.hasValue, isTrue);
        expect(state.value?.currentJourney, isNull);
        expect(state.value?.prompts, isEmpty);
        expect(state.value?.isDirty, isFalse);
      });

      test('should start new journey creation', () async {
        // Act
        await container
            .read(journeyBuilderNotifierProvider.notifier)
            .startNewJourney();

        // Assert
        final state = container.read(journeyBuilderNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.currentJourney, isNotNull);
        expect(state.value?.isDirty, isTrue);
      });

      test('should add prompt to journey', () async {
        // Arrange
        await container
            .read(journeyBuilderNotifierProvider.notifier)
            .startNewJourney();

        // Act
        await container
            .read(journeyBuilderNotifierProvider.notifier)
            .addPrompt('Test prompt', 'Test explanation');

        // Assert
        final state = container.read(journeyBuilderNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.prompts, hasLength(1));
        expect(state.value?.prompts.first.text, equals('Test prompt'));
        expect(state.value?.isDirty, isTrue);
      });

      test('should save journey successfully', () async {
        // Arrange
        final savedJourney = JourneyEntity(
          id: '1',
          title: 'Saved Journey',
          description: 'Saved Description',
          prompts: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockContentRepository.createJourney(argThat(isA<JourneyEntity>())))
            .thenAnswer((_) async => savedJourney);

        await container
            .read(journeyBuilderNotifierProvider.notifier)
            .startNewJourney();

        // Act
        await container
            .read(journeyBuilderNotifierProvider.notifier)
            .saveJourney('Saved Journey', 'Saved Description');

        // Assert
        final state = container.read(journeyBuilderNotifierProvider);
        expect(state.hasValue, isTrue);
        expect(state.value?.isDirty, isFalse);
        verify(mockContentRepository
                .createJourney(argThat(isA<JourneyEntity>())))
            .called(1);
      });
    });
  });
}
