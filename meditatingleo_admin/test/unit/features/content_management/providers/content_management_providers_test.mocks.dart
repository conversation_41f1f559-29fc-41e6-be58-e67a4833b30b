// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/features/content_management/providers/content_management_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:meditatingleo_admin/core/repositories/content_repository.dart'
    as _i4;
import 'package:meditatingleo_admin/features/content_management/domain/entities/journey_entity.dart'
    as _i3;
import 'package:meditatingleo_admin/shared/models/journey_content.dart' as _i6;
import 'package:meditatingleo_admin/shared/models/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T, E> extends _i1.SmartFake implements _i2.Result<T, E> {
  _FakeResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeJourneyEntity_1 extends _i1.SmartFake implements _i3.JourneyEntity {
  _FakeJourneyEntity_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ContentRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockContentRepository extends _i1.Mock implements _i4.ContentRepository {
  MockContentRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>> createJourneyContent(
          _i6.JourneyContent? journeyContent) =>
      (super.noSuchMethod(
        Invocation.method(
          #createJourneyContent,
          [journeyContent],
        ),
        returnValue:
            _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>.value(
                _FakeResult_0<_i6.JourneyContent, _i2.AppError>(
          this,
          Invocation.method(
            #createJourneyContent,
            [journeyContent],
          ),
        )),
      ) as _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      getAllJourneyContent() => (super.noSuchMethod(
            Invocation.method(
              #getAllJourneyContent,
              [],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #getAllJourneyContent,
                [],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>
      getJourneyContentById(String? id) => (super.noSuchMethod(
            Invocation.method(
              #getJourneyContentById,
              [id],
            ),
            returnValue:
                _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>.value(
                    _FakeResult_0<_i6.JourneyContent, _i2.AppError>(
              this,
              Invocation.method(
                #getJourneyContentById,
                [id],
              ),
            )),
          ) as _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>> updateJourneyContent(
          _i6.JourneyContent? journeyContent) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateJourneyContent,
          [journeyContent],
        ),
        returnValue:
            _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>.value(
                _FakeResult_0<_i6.JourneyContent, _i2.AppError>(
          this,
          Invocation.method(
            #updateJourneyContent,
            [journeyContent],
          ),
        )),
      ) as _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<void, _i2.AppError>> deleteJourneyContent(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteJourneyContent,
          [id],
        ),
        returnValue: _i5.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #deleteJourneyContent,
            [id],
          ),
        )),
      ) as _i5.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>
      publishJourneyContent(String? id) => (super.noSuchMethod(
            Invocation.method(
              #publishJourneyContent,
              [id],
            ),
            returnValue:
                _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>.value(
                    _FakeResult_0<_i6.JourneyContent, _i2.AppError>(
              this,
              Invocation.method(
                #publishJourneyContent,
                [id],
              ),
            )),
          ) as _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>
      unpublishJourneyContent(String? id) => (super.noSuchMethod(
            Invocation.method(
              #unpublishJourneyContent,
              [id],
            ),
            returnValue:
                _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>.value(
                    _FakeResult_0<_i6.JourneyContent, _i2.AppError>(
              this,
              Invocation.method(
                #unpublishJourneyContent,
                [id],
              ),
            )),
          ) as _i5.Future<_i2.Result<_i6.JourneyContent, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      getJourneyContentByCategory(String? category) => (super.noSuchMethod(
            Invocation.method(
              #getJourneyContentByCategory,
              [category],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #getJourneyContentByCategory,
                [category],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      getPublishedJourneyContent() => (super.noSuchMethod(
            Invocation.method(
              #getPublishedJourneyContent,
              [],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #getPublishedJourneyContent,
                [],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      getDraftJourneyContent() => (super.noSuchMethod(
            Invocation.method(
              #getDraftJourneyContent,
              [],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #getDraftJourneyContent,
                [],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<Map<String, dynamic>, _i2.AppError>>
      getContentAnalytics() => (super.noSuchMethod(
            Invocation.method(
              #getContentAnalytics,
              [],
            ),
            returnValue: _i5
                .Future<_i2.Result<Map<String, dynamic>, _i2.AppError>>.value(
                _FakeResult_0<Map<String, dynamic>, _i2.AppError>(
              this,
              Invocation.method(
                #getContentAnalytics,
                [],
              ),
            )),
          ) as _i5.Future<_i2.Result<Map<String, dynamic>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      searchJourneyContent(String? query) => (super.noSuchMethod(
            Invocation.method(
              #searchJourneyContent,
              [query],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #searchJourneyContent,
                [query],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>
      bulkUpdateJourneyContent(
    List<String>? ids,
    Map<String, dynamic>? updates,
  ) =>
          (super.noSuchMethod(
            Invocation.method(
              #bulkUpdateJourneyContent,
              [
                ids,
                updates,
              ],
            ),
            returnValue: _i5.Future<
                    _i2.Result<List<_i6.JourneyContent>, _i2.AppError>>.value(
                _FakeResult_0<List<_i6.JourneyContent>, _i2.AppError>(
              this,
              Invocation.method(
                #bulkUpdateJourneyContent,
                [
                  ids,
                  updates,
                ],
              ),
            )),
          ) as _i5.Future<_i2.Result<List<_i6.JourneyContent>, _i2.AppError>>);

  @override
  _i5.Future<_i2.Result<void, _i2.AppError>> bulkDeleteJourneyContent(
          List<String>? ids) =>
      (super.noSuchMethod(
        Invocation.method(
          #bulkDeleteJourneyContent,
          [ids],
        ),
        returnValue: _i5.Future<_i2.Result<void, _i2.AppError>>.value(
            _FakeResult_0<void, _i2.AppError>(
          this,
          Invocation.method(
            #bulkDeleteJourneyContent,
            [ids],
          ),
        )),
      ) as _i5.Future<_i2.Result<void, _i2.AppError>>);

  @override
  _i5.Future<List<_i3.JourneyEntity>> getAllJourneys() => (super.noSuchMethod(
        Invocation.method(
          #getAllJourneys,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.JourneyEntity>>.value(<_i3.JourneyEntity>[]),
      ) as _i5.Future<List<_i3.JourneyEntity>>);

  @override
  _i5.Future<_i3.JourneyEntity> createJourney(_i3.JourneyEntity? journey) =>
      (super.noSuchMethod(
        Invocation.method(
          #createJourney,
          [journey],
        ),
        returnValue: _i5.Future<_i3.JourneyEntity>.value(_FakeJourneyEntity_1(
          this,
          Invocation.method(
            #createJourney,
            [journey],
          ),
        )),
      ) as _i5.Future<_i3.JourneyEntity>);

  @override
  _i5.Future<_i3.JourneyEntity> updateJourney(_i3.JourneyEntity? journey) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateJourney,
          [journey],
        ),
        returnValue: _i5.Future<_i3.JourneyEntity>.value(_FakeJourneyEntity_1(
          this,
          Invocation.method(
            #updateJourney,
            [journey],
          ),
        )),
      ) as _i5.Future<_i3.JourneyEntity>);

  @override
  _i5.Future<void> deleteJourney(String? journeyId) => (super.noSuchMethod(
        Invocation.method(
          #deleteJourney,
          [journeyId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}
