import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/native.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';
import 'package:meditatingleo_admin/core/repositories/content_repository.dart';
import 'package:meditatingleo_admin/shared/models/journey_content.dart';
import 'package:meditatingleo_admin/shared/models/result.dart';
import 'package:meditatingleo_admin/shared/providers/database_providers.dart';
import 'package:meditatingleo_admin/shared/providers/repository_providers.dart';
import 'package:meditatingleo_admin/core/services/supabase_service.dart';
import 'package:mockito/mockito.dart';

// Mock SupabaseService for testing
class MockSupabaseService extends Mock implements SupabaseService {
  final Map<String, List<Map<String, dynamic>>> _tables = {};

  @override
  Future<Result<List<Map<String, dynamic>>, AppError>> insert(
    String tableName,
    Map<String, dynamic> data,
  ) async {
    _tables[tableName] ??= [];

    // Check for duplicate ID (simulate database constraint)
    final id = data['id'];
    if (id != null && _tables[tableName]!.any((item) => item['id'] == id)) {
      return Result.failure(
          AppError.database('Duplicate key value violates unique constraint'));
    }

    _tables[tableName]!.add(data);
    return Result.success([data]);
  }

  @override
  Future<Result<List<Map<String, dynamic>>, AppError>> query(
    String tableName,
  ) async {
    return Result.success(_tables[tableName] ?? []);
  }

  @override
  Future<Result<List<Map<String, dynamic>>, AppError>> queryById(
    String tableName,
    String id,
  ) async {
    final table = _tables[tableName] ?? [];
    final item = table.where((item) => item['id'] == id).toList();
    return Result.success(item);
  }

  @override
  Future<Result<List<Map<String, dynamic>>, AppError>> update(
    String tableName,
    String id,
    Map<String, dynamic> data,
  ) async {
    final table = _tables[tableName] ?? [];
    final index = table.indexWhere((item) => item['id'] == id);
    if (index != -1) {
      table[index] = {...table[index], ...data};
      return Result.success([table[index]]);
    }
    return Result.failure(AppError.notFound('Item not found'));
  }

  @override
  Future<Result<void, AppError>> delete(String tableName, String id) async {
    final table = _tables[tableName] ?? [];
    final index = table.indexWhere((item) => item['id'] == id);
    if (index != -1) {
      table.removeAt(index);
      return Result.success(null);
    }
    return Result.failure(AppError.notFound('Item not found'));
  }

  @override
  Future<Result<dynamic, AppError>> rpc(
    String functionName,
    Map<String, dynamic> params,
  ) async {
    // Mock RPC response
    return Result.success({
      'total_journeys': _tables['journey_content']?.length ?? 0,
      'published_journeys': 0,
      'draft_journeys': 0,
    });
  }
}

void main() {
  group('Admin Database Integration Tests', () {
    late ProviderContainer container;
    late AdminDatabase database;
    late MockSupabaseService mockSupabaseService;

    setUp(() async {
      // Create in-memory database for testing
      database = AdminDatabase(NativeDatabase.memory());
      mockSupabaseService = MockSupabaseService();

      container = ProviderContainer(
        overrides: [
          adminDatabaseProvider.overrideWithValue(database),
          supabaseServiceProvider.overrideWithValue(mockSupabaseService),
        ],
      );
    });

    tearDown(() async {
      await database.close();
      container.dispose();
    });

    group('Database and Repository Integration', () {
      test('should perform complete CRUD operations through repository',
          () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        final journeyContent = JourneyContent(
          id: 'integration-test-journey',
          title: 'Integration Test Journey',
          description: 'A journey created during integration testing',
          category: 'testing',
          difficulty: 'beginner',
          prompts: [
            'How do you feel about integration testing?',
            'What did you learn today?',
          ],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act & Assert - Create
        final createResult =
            await repository.createJourneyContent(journeyContent);
        expect(createResult.isSuccess, isTrue);
        expect(createResult.data?.id, equals('integration-test-journey'));

        // Act & Assert - Read
        final readResult =
            await repository.getJourneyContentById('integration-test-journey');
        expect(readResult.isSuccess, isTrue);
        expect(readResult.data?.title, equals('Integration Test Journey'));
        expect(readResult.data?.prompts.length, equals(2));

        // Act & Assert - Update
        final updatedJourney = readResult.data!.copyWith(
          title: 'Updated Integration Test Journey',
          isPublished: true,
          updatedAt: DateTime.now(),
        );

        final updateResult =
            await repository.updateJourneyContent(updatedJourney);
        expect(updateResult.isSuccess, isTrue);
        expect(updateResult.data?.title,
            equals('Updated Integration Test Journey'));
        expect(updateResult.data?.isPublished, isTrue);

        // Act & Assert - List all
        final listResult = await repository.getAllJourneyContent();
        expect(listResult.isSuccess, isTrue);
        expect(listResult.data?.length, equals(1));
        expect(listResult.data?.first.title,
            equals('Updated Integration Test Journey'));

        // Act & Assert - Delete
        final deleteResult =
            await repository.deleteJourneyContent('integration-test-journey');
        expect(deleteResult.isSuccess, isTrue);

        // Verify deletion
        final verifyResult = await repository.getAllJourneyContent();
        expect(verifyResult.isSuccess, isTrue);
        expect(verifyResult.data?.length, equals(0));
      });

      test('should handle multiple journey contents', () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        final journeys = List.generate(
            5,
            (index) => JourneyContent(
                  id: 'journey-$index',
                  title: 'Journey $index',
                  description: 'Description for journey $index',
                  category: index % 2 == 0 ? 'mindfulness' : 'gratitude',
                  difficulty: [
                    'beginner',
                    'intermediate',
                    'advanced'
                  ][index % 3],
                  prompts: ['Prompt ${index + 1}'],
                  isPublished: index % 2 == 0,
                  createdAt: DateTime.now().subtract(Duration(days: index)),
                  updatedAt: DateTime.now().subtract(Duration(days: index)),
                ));

        // Act - Create multiple journeys
        for (final journey in journeys) {
          final result = await repository.createJourneyContent(journey);
          expect(result.isSuccess, isTrue);
        }

        // Assert - Verify all journeys were created
        final allJourneys = await repository.getAllJourneyContent();
        expect(allJourneys.isSuccess, isTrue);
        expect(allJourneys.data?.length, equals(5));

        // Assert - Verify filtering by category
        final mindfulnessJourneys = allJourneys.data!
            .where((j) => j.category == 'mindfulness')
            .toList();
        expect(mindfulnessJourneys.length, equals(3)); // indices 0, 2, 4

        // Assert - Verify filtering by published status
        final publishedJourneys =
            allJourneys.data!.where((j) => j.isPublished).toList();
        expect(publishedJourneys.length, equals(3)); // indices 0, 2, 4

        // Assert - Verify filtering by difficulty
        final beginnerJourneys =
            allJourneys.data!.where((j) => j.difficulty == 'beginner').toList();
        expect(beginnerJourneys.length, equals(2)); // indices 0, 3
      });

      test('should handle concurrent operations', () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        // Act - Create multiple journeys concurrently
        final futures = List.generate(10, (index) {
          final journey = JourneyContent(
            id: 'concurrent-journey-$index',
            title: 'Concurrent Journey $index',
            description: 'Created concurrently',
            category: 'testing',
            difficulty: 'beginner',
            prompts: ['Concurrent prompt $index'],
            isPublished: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          return repository.createJourneyContent(journey);
        });

        final results = await Future.wait(futures);

        // Assert - All operations should succeed
        for (final result in results) {
          expect(result.isSuccess, isTrue);
        }

        // Verify all journeys were created
        final allJourneys = await repository.getAllJourneyContent();
        expect(allJourneys.isSuccess, isTrue);
        expect(allJourneys.data?.length, equals(10));
      });

      test('should maintain data integrity during complex operations',
          () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        final journey = JourneyContent(
          id: 'integrity-test-journey',
          title: 'Data Integrity Test',
          description: 'Testing data integrity',
          category: 'testing',
          difficulty: 'advanced',
          prompts: ['Initial prompt'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act - Create journey
        await repository.createJourneyContent(journey);

        // Act - Multiple rapid updates
        for (int i = 0; i < 5; i++) {
          final updatedJourney = journey.copyWith(
            title: 'Updated Title $i',
            prompts: ['Updated prompt $i'],
            updatedAt: DateTime.now(),
          );

          final result = await repository.updateJourneyContent(updatedJourney);
          expect(result.isSuccess, isTrue);
        }

        // Assert - Final state should be consistent
        final finalResult =
            await repository.getJourneyContentById('integrity-test-journey');
        expect(finalResult.isSuccess, isTrue);
        expect(finalResult.data?.title, equals('Updated Title 4'));
        expect(finalResult.data?.prompts.first, equals('Updated prompt 4'));

        // Act - Publish and unpublish operations
        await repository.publishJourneyContent('integrity-test-journey');
        final publishedResult =
            await repository.getJourneyContentById('integrity-test-journey');
        expect(publishedResult.data?.isPublished, isTrue);

        await repository.unpublishJourneyContent('integrity-test-journey');
        final unpublishedResult =
            await repository.getJourneyContentById('integrity-test-journey');
        expect(unpublishedResult.data?.isPublished, isFalse);
      });
    });

    group('Provider Integration', () {
      test('should work with Riverpod provider system', () async {
        // Arrange & Act
        final repository = container.read(contentRepositoryProvider);
        final database = container.read(adminDatabaseProvider);

        // Assert
        expect(repository, isNotNull);
        expect(database, isNotNull);
        expect(repository, isA<ContentRepository>());
        expect(database, isA<AdminDatabase>());
      });

      test('should maintain provider dependencies', () async {
        // Arrange
        final repository1 = container.read(contentRepositoryProvider);
        final repository2 = container.read(contentRepositoryProvider);

        // Assert - Should be the same instance (cached)
        expect(identical(repository1, repository2), isTrue);
      });

      test('should handle provider disposal gracefully', () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);
        expect(repository, isNotNull);

        // Act
        container.dispose();

        // Assert - Should not throw
        expect(() => container.dispose(), returnsNormally);
      });
    });

    group('Error Handling Integration', () {
      test('should handle database constraint violations', () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        final journey1 = JourneyContent(
          id: 'duplicate-id',
          title: 'First Journey',
          description: 'First description',
          category: 'testing',
          difficulty: 'beginner',
          prompts: ['First prompt'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final journey2 = JourneyContent(
          id: 'duplicate-id', // Same ID
          title: 'Second Journey',
          description: 'Second description',
          category: 'testing',
          difficulty: 'beginner',
          prompts: ['Second prompt'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Act
        final result1 = await repository.createJourneyContent(journey1);
        final result2 = await repository.createJourneyContent(journey2);

        // Assert
        expect(result1.isSuccess, isTrue);
        expect(result2.isFailure, isTrue); // Should fail due to duplicate ID
      });

      test('should handle non-existent record operations', () async {
        // Arrange
        final repository = container.read(contentRepositoryProvider);

        // Act & Assert - Get non-existent journey
        final getResult =
            await repository.getJourneyContentById('non-existent-id');
        expect(getResult.isFailure, isTrue);

        // Act & Assert - Update non-existent journey
        final nonExistentJourney = JourneyContent(
          id: 'non-existent-id',
          title: 'Non-existent',
          description: 'Does not exist',
          category: 'testing',
          difficulty: 'beginner',
          prompts: ['Prompt'],
          isPublished: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final updateResult =
            await repository.updateJourneyContent(nonExistentJourney);
        expect(updateResult.isFailure, isTrue);

        // Act & Assert - Delete non-existent journey
        final deleteResult =
            await repository.deleteJourneyContent('non-existent-id');
        expect(deleteResult.isFailure, isTrue);
      });
    });
  });
}
