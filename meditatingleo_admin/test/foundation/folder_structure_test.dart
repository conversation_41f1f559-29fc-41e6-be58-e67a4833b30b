import 'package:flutter_test/flutter_test.dart';
import 'dart:io';

/// Test suite for Admin Panel Folder Structure (TASK-001C)
/// 
/// This test suite validates that the feature-first folder structure
/// is properly implemented according to the coding standards.
/// 
/// Test Categories:
/// 1. Core Directory Structure
/// 2. Feature Directory Structure
/// 3. Shared Components Structure
/// 4. App-Level Structure
/// 5. Test Directory Structure
/// 6. Platform-Specific Structure
void main() {
  group('Admin Panel Folder Structure Tests', () {
    group('Core Directory Structure', () {
      test('should have lib directory with proper structure', () {
        final libDir = Directory('lib');
        expect(libDir.existsSync(), isTrue, reason: 'lib directory must exist');
      });

      test('should have main.dart in lib root', () {
        final mainFile = File('lib/main.dart');
        expect(mainFile.existsSync(), isTrue, reason: 'lib/main.dart must exist');
      });

      test('should have app directory for app-level configuration', () {
        final appDir = Directory('lib/app');
        expect(appDir.existsSync(), isTrue, reason: 'lib/app directory should exist');
      });

      test('should have core directory for shared utilities', () {
        final coreDir = Directory('lib/core');
        expect(coreDir.existsSync(), isTrue, reason: 'lib/core directory should exist');
      });

      test('should have features directory for feature modules', () {
        final featuresDir = Directory('lib/features');
        expect(featuresDir.existsSync(), isTrue, reason: 'lib/features directory should exist');
      });

      test('should have shared directory for shared components', () {
        final sharedDir = Directory('lib/shared');
        expect(sharedDir.existsSync(), isTrue, reason: 'lib/shared directory should exist');
      });
    });

    group('App-Level Structure', () {
      test('should have app.dart for main app configuration', () {
        final appFile = File('lib/app/app.dart');
        expect(appFile.existsSync(), isTrue, reason: 'lib/app/app.dart should exist');
      });

      test('should have router directory for navigation', () {
        final routerDir = Directory('lib/app/router');
        expect(routerDir.existsSync(), isTrue, reason: 'lib/app/router directory should exist');
      });

      test('should have app_router.dart for routing configuration', () {
        final routerFile = File('lib/app/router/app_router.dart');
        expect(routerFile.existsSync(), isTrue, reason: 'lib/app/router/app_router.dart should exist');
      });

      test('should have routes.dart for route definitions', () {
        final routesFile = File('lib/app/router/routes.dart');
        expect(routesFile.existsSync(), isTrue, reason: 'lib/app/router/routes.dart should exist');
      });
    });

    group('Core Directory Structure', () {
      test('should have constants directory', () {
        final constantsDir = Directory('lib/core/constants');
        expect(constantsDir.existsSync(), isTrue, reason: 'lib/core/constants directory should exist');
      });

      test('should have app_constants.dart', () {
        final constantsFile = File('lib/core/constants/app_constants.dart');
        expect(constantsFile.existsSync(), isTrue, reason: 'lib/core/constants/app_constants.dart should exist');
      });

      test('should have theme directory', () {
        final themeDir = Directory('lib/core/theme');
        expect(themeDir.existsSync(), isTrue, reason: 'lib/core/theme directory should exist');
      });

      test('should have app_theme.dart', () {
        final themeFile = File('lib/core/theme/app_theme.dart');
        expect(themeFile.existsSync(), isTrue, reason: 'lib/core/theme/app_theme.dart should exist');
      });

      test('should have utils directory', () {
        final utilsDir = Directory('lib/core/utils');
        expect(utilsDir.existsSync(), isTrue, reason: 'lib/core/utils directory should exist');
      });

      test('should have widgets directory for core widgets', () {
        final widgetsDir = Directory('lib/core/widgets');
        expect(widgetsDir.existsSync(), isTrue, reason: 'lib/core/widgets directory should exist');
      });

      test('should have extensions directory', () {
        final extensionsDir = Directory('lib/core/extensions');
        expect(extensionsDir.existsSync(), isTrue, reason: 'lib/core/extensions directory should exist');
      });
    });

    group('Feature Directory Structure', () {
      test('should have authentication feature directory', () {
        final authDir = Directory('lib/features/authentication');
        expect(authDir.existsSync(), isTrue, reason: 'lib/features/authentication directory should exist');
      });

      test('should have content_management feature directory', () {
        final contentDir = Directory('lib/features/content_management');
        expect(contentDir.existsSync(), isTrue, reason: 'lib/features/content_management directory should exist');
      });

      test('should have user_management feature directory', () {
        final userMgmtDir = Directory('lib/features/user_management');
        expect(userMgmtDir.existsSync(), isTrue, reason: 'lib/features/user_management directory should exist');
      });

      test('should have system_admin feature directory', () {
        final systemAdminDir = Directory('lib/features/system_admin');
        expect(systemAdminDir.existsSync(), isTrue, reason: 'lib/features/system_admin directory should exist');
      });

      group('Authentication Feature Structure', () {
        test('should have data layer in authentication feature', () {
          final dataDir = Directory('lib/features/authentication/data');
          expect(dataDir.existsSync(), isTrue, reason: 'authentication data layer should exist');
        });

        test('should have presentation layer in authentication feature', () {
          final presentationDir = Directory('lib/features/authentication/presentation');
          expect(presentationDir.existsSync(), isTrue, reason: 'authentication presentation layer should exist');
        });

        test('should have domain layer in authentication feature', () {
          final domainDir = Directory('lib/features/authentication/domain');
          expect(domainDir.existsSync(), isTrue, reason: 'authentication domain layer should exist');
        });

        test('should have models in authentication data layer', () {
          final modelsDir = Directory('lib/features/authentication/data/models');
          expect(modelsDir.existsSync(), isTrue, reason: 'authentication models directory should exist');
        });

        test('should have repositories in authentication data layer', () {
          final reposDir = Directory('lib/features/authentication/data/repositories');
          expect(reposDir.existsSync(), isTrue, reason: 'authentication repositories directory should exist');
        });

        test('should have services in authentication data layer', () {
          final servicesDir = Directory('lib/features/authentication/data/services');
          expect(servicesDir.existsSync(), isTrue, reason: 'authentication services directory should exist');
        });

        test('should have pages in authentication presentation layer', () {
          final pagesDir = Directory('lib/features/authentication/presentation/pages');
          expect(pagesDir.existsSync(), isTrue, reason: 'authentication pages directory should exist');
        });

        test('should have widgets in authentication presentation layer', () {
          final widgetsDir = Directory('lib/features/authentication/presentation/widgets');
          expect(widgetsDir.existsSync(), isTrue, reason: 'authentication widgets directory should exist');
        });

        test('should have providers in authentication presentation layer', () {
          final providersDir = Directory('lib/features/authentication/presentation/providers');
          expect(providersDir.existsSync(), isTrue, reason: 'authentication providers directory should exist');
        });
      });
    });

    group('Shared Components Structure', () {
      test('should have models in shared directory', () {
        final modelsDir = Directory('lib/shared/models');
        expect(modelsDir.existsSync(), isTrue, reason: 'lib/shared/models directory should exist');
      });

      test('should have services in shared directory', () {
        final servicesDir = Directory('lib/shared/services');
        expect(servicesDir.existsSync(), isTrue, reason: 'lib/shared/services directory should exist');
      });

      test('should have providers in shared directory', () {
        final providersDir = Directory('lib/shared/providers');
        expect(providersDir.existsSync(), isTrue, reason: 'lib/shared/providers directory should exist');
      });

      test('should have supabase_service.dart in shared services', () {
        final supabaseFile = File('lib/shared/services/supabase_service.dart');
        expect(supabaseFile.existsSync(), isTrue, reason: 'lib/shared/services/supabase_service.dart should exist');
      });
    });

    group('Test Directory Structure', () {
      test('should have test directory', () {
        final testDir = Directory('test');
        expect(testDir.existsSync(), isTrue, reason: 'test directory must exist');
      });

      test('should have unit test directory', () {
        final unitDir = Directory('test/unit');
        expect(unitDir.existsSync(), isTrue, reason: 'test/unit directory should exist');
      });

      test('should have widget test directory', () {
        final widgetDir = Directory('test/widget');
        expect(widgetDir.existsSync(), isTrue, reason: 'test/widget directory should exist');
      });

      test('should have integration test directory', () {
        final integrationDir = Directory('test/integration');
        expect(integrationDir.existsSync(), isTrue, reason: 'test/integration directory should exist');
      });

      test('should have foundation test directory', () {
        final foundationDir = Directory('test/foundation');
        expect(foundationDir.existsSync(), isTrue, reason: 'test/foundation directory should exist');
      });
    });

    group('Build Configuration Structure', () {
      test('should have build.yaml for code generation', () {
        final buildFile = File('build.yaml');
        expect(buildFile.existsSync(), isTrue, reason: 'build.yaml should exist for code generation');
      });

      test('should have analysis_options.yaml for linting', () {
        final analysisFile = File('analysis_options.yaml');
        expect(analysisFile.existsSync(), isTrue, reason: 'analysis_options.yaml should exist');
      });
    });

    group('Platform-Specific Structure Validation', () {
      test('should have web directory for web deployment', () {
        final webDir = Directory('web');
        expect(webDir.existsSync(), isTrue, reason: 'web directory should exist for admin panel deployment');
      });

      test('should have proper web manifest for PWA', () {
        final manifestFile = File('web/manifest.json');
        expect(manifestFile.existsSync(), isTrue, reason: 'web/manifest.json should exist');
      });

      test('should have web index.html', () {
        final indexFile = File('web/index.html');
        expect(indexFile.existsSync(), isTrue, reason: 'web/index.html should exist');
      });
    });
  });
}
