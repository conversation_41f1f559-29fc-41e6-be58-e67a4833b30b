import 'package:flutter_test/flutter_test.dart';
import 'dart:io';
import 'package:yaml/yaml.dart';

/// Test suite for Admin Panel Dependency Configuration (TASK-001C)
///
/// This test suite validates that all required dependencies are properly
/// configured in pubspec.yaml for the admin panel application.
///
/// Test Categories:
/// 1. Core Flutter Dependencies
/// 2. State Management Dependencies (Riverpod)
/// 3. Database Dependencies (Drift)
/// 4. Backend Dependencies (Supabase)
/// 5. UI/UX Dependencies (Material Design 3)
/// 6. Development Dependencies
/// 7. Platform-Specific Dependencies
/// 8. Admin-Specific Dependencies
/// Helper function to convert YAML to Map<String, dynamic>
Map<String, dynamic> _convertYamlToMap(dynamic yaml) {
  if (yaml is Map) {
    return Map<String, dynamic>.from(yaml);
  }
  return <String, dynamic>{};
}

void main() {
  group('Admin Panel Dependency Configuration Tests', () {
    late Map<String, dynamic> pubspecContent;
    late Map<String, dynamic> dependencies;
    late Map<String, dynamic> devDependencies;

    setUpAll(() async {
      // Load and parse pubspec.yaml
      final pubspecFile = File('pubspec.yaml');
      expect(pubspecFile.existsSync(), isTrue,
          reason: 'pubspec.yaml must exist');

      final pubspecString = await pubspecFile.readAsString();
      final pubspecYaml = loadYaml(pubspecString);
      pubspecContent = _convertYamlToMap(pubspecYaml);

      dependencies = _convertYamlToMap(pubspecContent['dependencies'] ?? {});
      devDependencies =
          _convertYamlToMap(pubspecContent['dev_dependencies'] ?? {});
    });

    group('Core Flutter Dependencies', () {
      test('should have Flutter SDK dependency', () {
        expect(dependencies, containsPair('flutter', {'sdk': 'flutter'}));
      });

      test('should specify correct Dart SDK version', () {
        final environment = _convertYamlToMap(pubspecContent['environment']);
        expect(environment, isNotNull);
        expect(environment['sdk'], isNotNull);

        // Should use modern Dart version (3.5.3+)
        final sdkConstraint = environment['sdk'] as String;
        expect(sdkConstraint, contains('3.5.3'));
      });

      test('should have Material Design enabled', () {
        final flutter = _convertYamlToMap(pubspecContent['flutter']);
        expect(flutter, isNotNull);
        expect(flutter['uses-material-design'], isTrue);
      });
    });

    group('State Management Dependencies (Riverpod)', () {
      test('should include flutter_riverpod for state management', () {
        expect(dependencies, contains('flutter_riverpod'));
      });

      test('should include riverpod_annotation for code generation', () {
        expect(dependencies, contains('riverpod_annotation'));
      });

      test('should include riverpod_generator in dev dependencies', () {
        expect(devDependencies, contains('riverpod_generator'));
      });

      test('should include build_runner for code generation', () {
        expect(devDependencies, contains('build_runner'));
      });
    });

    group('Database Dependencies (Drift)', () {
      test('should include drift for local database', () {
        expect(dependencies, contains('drift'));
      });

      test('should include sqlite3_flutter_libs for SQLite support', () {
        expect(dependencies, contains('sqlite3_flutter_libs'));
      });

      test('should include drift_dev in dev dependencies', () {
        expect(devDependencies, contains('drift_dev'));
      });

      test('should include path_provider for database storage', () {
        expect(dependencies, contains('path_provider'));
      });
    });

    group('Backend Dependencies (Supabase)', () {
      test('should include supabase_flutter for backend integration', () {
        expect(dependencies, contains('supabase_flutter'));
      });

      test('should include http for API requests', () {
        expect(dependencies, contains('http'));
      });
    });

    group('UI/UX Dependencies', () {
      test('should include go_router for navigation', () {
        expect(dependencies, contains('go_router'));
      });

      test('should include flutter_svg for vector graphics', () {
        expect(dependencies, contains('flutter_svg'));
      });

      test('should include cached_network_image for image caching', () {
        expect(dependencies, contains('cached_network_image'));
      });

      test('should include google_fonts for typography', () {
        expect(dependencies, contains('google_fonts'));
      });
    });

    group('Data Handling Dependencies', () {
      test('should include freezed_annotation for immutable models', () {
        expect(dependencies, contains('freezed_annotation'));
      });

      test('should include json_annotation for JSON serialization', () {
        expect(dependencies, contains('json_annotation'));
      });

      test('should include freezed in dev dependencies', () {
        expect(devDependencies, contains('freezed'));
      });

      test('should include json_serializable in dev dependencies', () {
        expect(devDependencies, contains('json_serializable'));
      });
    });

    group('Utility Dependencies', () {
      test('should include intl for internationalization', () {
        expect(dependencies, contains('intl'));
      });

      test('should include uuid for unique identifiers', () {
        expect(dependencies, contains('uuid'));
      });

      test('should include equatable for value equality', () {
        expect(dependencies, contains('equatable'));
      });
    });

    group('Development Dependencies', () {
      test('should include flutter_test for testing', () {
        expect(
            devDependencies, containsPair('flutter_test', {'sdk': 'flutter'}));
      });

      test('should include flutter_lints for code quality', () {
        expect(devDependencies, contains('flutter_lints'));
      });

      test('should include mockito for mocking in tests', () {
        expect(devDependencies, contains('mockito'));
      });

      test('should include build_runner for code generation', () {
        expect(devDependencies, contains('build_runner'));
      });
    });

    group('Admin-Specific Dependencies', () {
      test('should include file_picker for file management', () {
        expect(dependencies, contains('file_picker'));
      });

      test('should include csv for data export', () {
        expect(dependencies, contains('csv'));
      });

      test('should include flutter_quill for rich text editing', () {
        expect(dependencies, contains('flutter_quill'));
      });

      test('should include data_table_2 for advanced tables', () {
        expect(dependencies, contains('data_table_2'));
      });
    });

    group('Analytics and Monitoring Dependencies', () {
      test('should include sentry_flutter for error tracking', () {
        expect(dependencies, contains('sentry_flutter'));
      });

      test('should include package_info_plus for app information', () {
        expect(dependencies, contains('package_info_plus'));
      });
    });

    group('Desktop-Specific Dependencies', () {
      // Desktop dependencies commented out for web compatibility
      test('should not include window_manager for web compatibility', () {
        expect(dependencies, isNot(contains('window_manager')));
      });

      test('should not include desktop_window for web compatibility', () {
        expect(dependencies, isNot(contains('desktop_window')));
      });
    });

    group('Dependency Version Constraints', () {
      test('should use appropriate version constraints', () {
        // Verify that dependencies use proper version constraints
        // This ensures compatibility and stability

        dependencies.forEach((key, value) {
          if (value is String) {
            // Version constraints should be reasonable
            expect(value, isNotEmpty,
                reason: 'Version constraint for $key should not be empty');
          }
        });
      });

      test('should not have conflicting dependencies', () {
        // Verify no obvious conflicts between dependencies
        // This is a basic check - more detailed conflict resolution
        // will be handled by pub get

        expect(dependencies, isNotEmpty);
        expect(devDependencies, isNotEmpty);
      });
    });

    group('Build Configuration', () {
      test('should have proper build configuration', () {
        // Verify build.yaml exists or will be created for code generation
        // This test validates the foundation for code generation setup

        expect(pubspecContent['name'], equals('meditatingleo_admin'));
        expect(pubspecContent['description'], isNotNull);
        expect(pubspecContent['version'], isNotNull);
      });
    });
  });
}
