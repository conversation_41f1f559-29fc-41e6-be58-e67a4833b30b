/// [Validators] provides static methods for validating user input.
///
/// This utility class contains validation logic for common form fields
/// used throughout the admin application, ensuring consistent validation
/// rules and error messages.
///
/// Features:
/// - Email validation with RFC-compliant regex
/// - Password strength validation
/// - Required field validation
/// - Custom validation patterns
/// - Consistent error messaging
class Validators {
  // Private constructor to prevent instantiation
  Validators._();

  /// Email validation regex pattern (simplified but robust)
  static final RegExp _emailRegex = RegExp(
    r'^[\w\-\.]+@([\w\-]+\.)+[\w\-]{2,4}$',
  );

  /// Password validation regex pattern (at least one letter, one number)
  static final RegExp _passwordRegex = RegExp(
    r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$',
  );

  /// Validates an email address format.
  ///
  /// Returns `null` if the email is valid, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [email]: The email address to validate
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.email('<EMAIL>'); // Returns null
  /// final error = Validators.email('invalid'); // Returns error message
  /// ```
  static String? email(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }

    if (email.length > 254) {
      return 'Email is too long';
    }

    if (!_emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates a password strength.
  ///
  /// Returns `null` if the password is valid, otherwise returns an error message.
  ///
  /// Password requirements:
  /// - At least 8 characters long
  /// - Contains at least one letter
  /// - Contains at least one number
  /// - May contain special characters
  ///
  /// Parameters:
  /// - [password]: The password to validate
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.password('Password123'); // Returns null
  /// final error = Validators.password('weak'); // Returns error message
  /// ```
  static String? password(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (password.length > 128) {
      return 'Password is too long (maximum 128 characters)';
    }

    if (!password.contains(RegExp(r'[A-Za-z]'))) {
      return 'Password must contain at least one letter';
    }

    if (!password.contains(RegExp(r'\d'))) {
      return 'Password must contain at least one number';
    }

    return null;
  }

  /// Validates a required field.
  ///
  /// Returns `null` if the field has a value, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.required('John', 'Name'); // Returns null
  /// final error = Validators.required('', 'Name'); // Returns 'Name is required'
  /// ```
  static String? required(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validates a minimum length requirement.
  ///
  /// Returns `null` if the value meets the minimum length, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [minLength]: The minimum required length
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.minLength('Hello', 3, 'Message'); // Returns null
  /// final error = Validators.minLength('Hi', 5, 'Message'); // Returns error
  /// ```
  static String? minLength(String? value, int minLength, String fieldName) {
    if (value == null || value.length < minLength) {
      return '$fieldName must be at least $minLength characters long';
    }
    return null;
  }

  /// Validates a maximum length requirement.
  ///
  /// Returns `null` if the value is within the maximum length, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [maxLength]: The maximum allowed length
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.maxLength('Hello', 10, 'Message'); // Returns null
  /// final error = Validators.maxLength('Very long message', 5, 'Message'); // Returns error
  /// ```
  static String? maxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.length > maxLength) {
      return '$fieldName must be no more than $maxLength characters long';
    }
    return null;
  }

  /// Validates that a value matches a specific pattern.
  ///
  /// Returns `null` if the value matches the pattern, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [pattern]: The regex pattern to match against
  /// - [errorMessage]: The error message to return if validation fails
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.pattern('123', RegExp(r'^\d+$'), 'Must be numbers only');
  /// ```
  static String? pattern(String? value, RegExp pattern, String errorMessage) {
    if (value == null || !pattern.hasMatch(value)) {
      return errorMessage;
    }
    return null;
  }

  /// Validates that two values match (useful for password confirmation).
  ///
  /// Returns `null` if the values match, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The first value
  /// - [confirmValue]: The value to compare against
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.match('password', 'password', 'Password');
  /// final error = Validators.match('password', 'different', 'Password');
  /// ```
  static String? match(String? value, String? confirmValue, String fieldName) {
    if (value != confirmValue) {
      return '$fieldName confirmation does not match';
    }
    return null;
  }

  /// Validates a URL format.
  ///
  /// Returns `null` if the URL is valid, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [url]: The URL to validate
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.url('https://example.com'); // Returns null
  /// final error = Validators.url('invalid-url'); // Returns error message
  /// ```
  static String? url(String? url) {
    if (url == null || url.isEmpty) {
      return 'URL is required';
    }

    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return 'Please enter a valid URL (must start with http:// or https://)';
      }
      return null;
    } catch (e) {
      return 'Please enter a valid URL';
    }
  }

  /// Validates a phone number format (basic validation).
  ///
  /// Returns `null` if the phone number is valid, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [phone]: The phone number to validate
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.phone('+1234567890'); // Returns null
  /// final error = Validators.phone('invalid'); // Returns error message
  /// ```
  static String? phone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'Phone number is required';
    }

    // Remove all non-digit characters for validation
    final digitsOnly = phone.replaceAll(RegExp(r'\D'), '');

    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validates a numeric value.
  ///
  /// Returns `null` if the value is a valid number, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.numeric('123', 'Age'); // Returns null
  /// final error = Validators.numeric('abc', 'Age'); // Returns error message
  /// ```
  static String? numeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }

    if (double.tryParse(value) == null) {
      return '$fieldName must be a valid number';
    }

    return null;
  }

  /// Validates that a numeric value is within a specific range.
  ///
  /// Returns `null` if the value is within range, otherwise returns an error message.
  ///
  /// Parameters:
  /// - [value]: The value to validate
  /// - [min]: The minimum allowed value
  /// - [max]: The maximum allowed value
  /// - [fieldName]: The name of the field for error messaging
  ///
  /// Returns:
  /// - `null` if valid
  /// - Error message string if invalid
  ///
  /// Example:
  /// ```dart
  /// final error = Validators.range('25', 18, 65, 'Age'); // Returns null
  /// final error = Validators.range('100', 18, 65, 'Age'); // Returns error message
  /// ```
  static String? range(
      String? value, double min, double max, String fieldName) {
    final numericError = numeric(value, fieldName);
    if (numericError != null) return numericError;

    final numValue = double.parse(value!);
    if (numValue < min || numValue > max) {
      return '$fieldName must be between $min and $max';
    }

    return null;
  }
}
