import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'admin_database.g.dart';

// Table definitions
@DataClassName('JourneyContentData')
class JourneyContentTable extends Table {
  TextColumn get id => text()();
  TextColumn get title => text()();
  TextColumn get description => text()();
  TextColumn get category => text()();
  TextColumn get difficulty => text()();
  TextColumn get prompts => text().map(const StringListConverter())();
  BoolColumn get isPublished => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('AdminUserData')
class AdminUsers extends Table {
  TextColumn get id => text()();
  TextColumn get email => text().unique()();
  TextColumn get role => text()();
  TextColumn get permissions => text().map(const StringListConverter())();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get lastLoginAt => dateTime().nullable()();
  DateTimeColumn get createdAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('SystemMetricsData')
class SystemMetrics extends Table {
  TextColumn get id => text()();
  TextColumn get metricType => text()();
  RealColumn get value => real()();
  TextColumn get metadata => text().map(const JsonConverter())();
  DateTimeColumn get recordedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

// Custom converters
class StringListConverter extends TypeConverter<List<String>, String> {
  const StringListConverter();

  @override
  List<String> fromSql(String fromDb) {
    if (fromDb.isEmpty) return [];
    return fromDb.split(',').map((e) => e.trim()).toList();
  }

  @override
  String toSql(List<String> value) {
    return value.join(',');
  }
}

class JsonConverter extends TypeConverter<Map<String, dynamic>, String> {
  const JsonConverter();

  @override
  Map<String, dynamic> fromSql(String fromDb) {
    if (fromDb.isEmpty) return {};
    try {
      return Map<String, dynamic>.from(
        Uri.splitQueryString(fromDb),
      );
    } catch (e) {
      return {};
    }
  }

  @override
  String toSql(Map<String, dynamic> value) {
    return Uri(queryParameters: value.map((k, v) => MapEntry(k, v.toString())))
        .query;
  }
}

// Database class
@DriftDatabase(tables: [JourneyContentTable, AdminUsers, SystemMetrics])
class AdminDatabase extends _$AdminDatabase {
  AdminDatabase(QueryExecutor e) : super(e);

  // Factory constructor for file-based database
  factory AdminDatabase.file() {
    return AdminDatabase(_openConnection());
  }

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle future migrations here
      },
    );
  }

  // Journey Content operations
  Future<int> insertJourneyContent(JourneyContentDataCompanion journey) {
    return into(journeyContentTable).insert(journey);
  }

  Future<List<JourneyContentData>> getAllJourneyContent() {
    return select(journeyContentTable).get();
  }

  Future<JourneyContentData?> getJourneyContentById(String id) {
    return (select(journeyContentTable)..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }

  Future<bool> updateJourneyContent(
      String id, JourneyContentDataCompanion journey) async {
    final result = await (update(journeyContentTable)
          ..where((tbl) => tbl.id.equals(id)))
        .write(journey);
    return result > 0;
  }

  Future<bool> deleteJourneyContent(String id) async {
    final result = await (delete(journeyContentTable)
          ..where((tbl) => tbl.id.equals(id)))
        .go();
    return result > 0;
  }

  // Admin User operations
  Future<int> insertAdminUser(AdminUserDataCompanion user) {
    return into(adminUsers).insert(user);
  }

  Future<List<AdminUserData>> getAllAdminUsers() {
    return select(adminUsers).get();
  }

  Future<AdminUserData?> getAdminUserById(String id) {
    return (select(adminUsers)..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }

  // System Metrics operations
  Future<int> insertSystemMetrics(SystemMetricsDataCompanion metrics) {
    return into(systemMetrics).insert(metrics);
  }

  Future<List<SystemMetricsData>> getAllSystemMetrics() {
    return select(systemMetrics).get();
  }

  Future<List<SystemMetricsData>> getSystemMetricsByType(String metricType) {
    return (select(systemMetrics)
          ..where((tbl) => tbl.metricType.equals(metricType)))
        .get();
  }
}

// Database connection helper
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'admin_database.db'));
    return NativeDatabase.createInBackground(file);
  });
}
