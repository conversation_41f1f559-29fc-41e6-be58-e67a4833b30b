// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_database.dart';

// ignore_for_file: type=lint
class $JourneyContentTableTable extends JourneyContentTable
    with TableInfo<$JourneyContentTableTable, JourneyContentData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $JourneyContentTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _categoryMeta =
      const VerificationMeta('category');
  @override
  late final GeneratedColumn<String> category = GeneratedColumn<String>(
      'category', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _difficultyMeta =
      const VerificationMeta('difficulty');
  @override
  late final GeneratedColumn<String> difficulty = GeneratedColumn<String>(
      'difficulty', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<List<String>, String> prompts =
      GeneratedColumn<String>('prompts', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<List<String>>(
              $JourneyContentTableTable.$converterprompts);
  static const VerificationMeta _isPublishedMeta =
      const VerificationMeta('isPublished');
  @override
  late final GeneratedColumn<bool> isPublished = GeneratedColumn<bool>(
      'is_published', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_published" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        title,
        description,
        category,
        difficulty,
        prompts,
        isPublished,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'journey_content_table';
  @override
  VerificationContext validateIntegrity(Insertable<JourneyContentData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    } else if (isInserting) {
      context.missing(_descriptionMeta);
    }
    if (data.containsKey('category')) {
      context.handle(_categoryMeta,
          category.isAcceptableOrUnknown(data['category']!, _categoryMeta));
    } else if (isInserting) {
      context.missing(_categoryMeta);
    }
    if (data.containsKey('difficulty')) {
      context.handle(
          _difficultyMeta,
          difficulty.isAcceptableOrUnknown(
              data['difficulty']!, _difficultyMeta));
    } else if (isInserting) {
      context.missing(_difficultyMeta);
    }
    if (data.containsKey('is_published')) {
      context.handle(
          _isPublishedMeta,
          isPublished.isAcceptableOrUnknown(
              data['is_published']!, _isPublishedMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  JourneyContentData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return JourneyContentData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description'])!,
      category: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category'])!,
      difficulty: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}difficulty'])!,
      prompts: $JourneyContentTableTable.$converterprompts.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}prompts'])!),
      isPublished: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_published'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $JourneyContentTableTable createAlias(String alias) {
    return $JourneyContentTableTable(attachedDatabase, alias);
  }

  static TypeConverter<List<String>, String> $converterprompts =
      const StringListConverter();
}

class JourneyContentData extends DataClass
    implements Insertable<JourneyContentData> {
  final String id;
  final String title;
  final String description;
  final String category;
  final String difficulty;
  final List<String> prompts;
  final bool isPublished;
  final DateTime createdAt;
  final DateTime updatedAt;
  const JourneyContentData(
      {required this.id,
      required this.title,
      required this.description,
      required this.category,
      required this.difficulty,
      required this.prompts,
      required this.isPublished,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['title'] = Variable<String>(title);
    map['description'] = Variable<String>(description);
    map['category'] = Variable<String>(category);
    map['difficulty'] = Variable<String>(difficulty);
    {
      map['prompts'] = Variable<String>(
          $JourneyContentTableTable.$converterprompts.toSql(prompts));
    }
    map['is_published'] = Variable<bool>(isPublished);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  JourneyContentDataCompanion toCompanion(bool nullToAbsent) {
    return JourneyContentDataCompanion(
      id: Value(id),
      title: Value(title),
      description: Value(description),
      category: Value(category),
      difficulty: Value(difficulty),
      prompts: Value(prompts),
      isPublished: Value(isPublished),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory JourneyContentData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return JourneyContentData(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      description: serializer.fromJson<String>(json['description']),
      category: serializer.fromJson<String>(json['category']),
      difficulty: serializer.fromJson<String>(json['difficulty']),
      prompts: serializer.fromJson<List<String>>(json['prompts']),
      isPublished: serializer.fromJson<bool>(json['isPublished']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'description': serializer.toJson<String>(description),
      'category': serializer.toJson<String>(category),
      'difficulty': serializer.toJson<String>(difficulty),
      'prompts': serializer.toJson<List<String>>(prompts),
      'isPublished': serializer.toJson<bool>(isPublished),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  JourneyContentData copyWith(
          {String? id,
          String? title,
          String? description,
          String? category,
          String? difficulty,
          List<String>? prompts,
          bool? isPublished,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      JourneyContentData(
        id: id ?? this.id,
        title: title ?? this.title,
        description: description ?? this.description,
        category: category ?? this.category,
        difficulty: difficulty ?? this.difficulty,
        prompts: prompts ?? this.prompts,
        isPublished: isPublished ?? this.isPublished,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  JourneyContentData copyWithCompanion(JourneyContentDataCompanion data) {
    return JourneyContentData(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      description:
          data.description.present ? data.description.value : this.description,
      category: data.category.present ? data.category.value : this.category,
      difficulty:
          data.difficulty.present ? data.difficulty.value : this.difficulty,
      prompts: data.prompts.present ? data.prompts.value : this.prompts,
      isPublished:
          data.isPublished.present ? data.isPublished.value : this.isPublished,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('JourneyContentData(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('description: $description, ')
          ..write('category: $category, ')
          ..write('difficulty: $difficulty, ')
          ..write('prompts: $prompts, ')
          ..write('isPublished: $isPublished, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, title, description, category, difficulty,
      prompts, isPublished, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is JourneyContentData &&
          other.id == this.id &&
          other.title == this.title &&
          other.description == this.description &&
          other.category == this.category &&
          other.difficulty == this.difficulty &&
          other.prompts == this.prompts &&
          other.isPublished == this.isPublished &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class JourneyContentDataCompanion extends UpdateCompanion<JourneyContentData> {
  final Value<String> id;
  final Value<String> title;
  final Value<String> description;
  final Value<String> category;
  final Value<String> difficulty;
  final Value<List<String>> prompts;
  final Value<bool> isPublished;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const JourneyContentDataCompanion({
    this.id = const Value.absent(),
    this.title = const Value.absent(),
    this.description = const Value.absent(),
    this.category = const Value.absent(),
    this.difficulty = const Value.absent(),
    this.prompts = const Value.absent(),
    this.isPublished = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  JourneyContentDataCompanion.insert({
    required String id,
    required String title,
    required String description,
    required String category,
    required String difficulty,
    required List<String> prompts,
    this.isPublished = const Value.absent(),
    required DateTime createdAt,
    required DateTime updatedAt,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        title = Value(title),
        description = Value(description),
        category = Value(category),
        difficulty = Value(difficulty),
        prompts = Value(prompts),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt);
  static Insertable<JourneyContentData> custom({
    Expression<String>? id,
    Expression<String>? title,
    Expression<String>? description,
    Expression<String>? category,
    Expression<String>? difficulty,
    Expression<String>? prompts,
    Expression<bool>? isPublished,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (category != null) 'category': category,
      if (difficulty != null) 'difficulty': difficulty,
      if (prompts != null) 'prompts': prompts,
      if (isPublished != null) 'is_published': isPublished,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  JourneyContentDataCompanion copyWith(
      {Value<String>? id,
      Value<String>? title,
      Value<String>? description,
      Value<String>? category,
      Value<String>? difficulty,
      Value<List<String>>? prompts,
      Value<bool>? isPublished,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return JourneyContentDataCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      prompts: prompts ?? this.prompts,
      isPublished: isPublished ?? this.isPublished,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (category.present) {
      map['category'] = Variable<String>(category.value);
    }
    if (difficulty.present) {
      map['difficulty'] = Variable<String>(difficulty.value);
    }
    if (prompts.present) {
      map['prompts'] = Variable<String>(
          $JourneyContentTableTable.$converterprompts.toSql(prompts.value));
    }
    if (isPublished.present) {
      map['is_published'] = Variable<bool>(isPublished.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('JourneyContentDataCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('description: $description, ')
          ..write('category: $category, ')
          ..write('difficulty: $difficulty, ')
          ..write('prompts: $prompts, ')
          ..write('isPublished: $isPublished, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AdminUsersTable extends AdminUsers
    with TableInfo<$AdminUsersTable, AdminUserData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AdminUsersTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _roleMeta = const VerificationMeta('role');
  @override
  late final GeneratedColumn<String> role = GeneratedColumn<String>(
      'role', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<List<String>, String>
      permissions = GeneratedColumn<String>('permissions', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<List<String>>($AdminUsersTable.$converterpermissions);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _lastLoginAtMeta =
      const VerificationMeta('lastLoginAt');
  @override
  late final GeneratedColumn<DateTime> lastLoginAt = GeneratedColumn<DateTime>(
      'last_login_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [id, email, role, permissions, isActive, lastLoginAt, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'admin_users';
  @override
  VerificationContext validateIntegrity(Insertable<AdminUserData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    } else if (isInserting) {
      context.missing(_emailMeta);
    }
    if (data.containsKey('role')) {
      context.handle(
          _roleMeta, role.isAcceptableOrUnknown(data['role']!, _roleMeta));
    } else if (isInserting) {
      context.missing(_roleMeta);
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    if (data.containsKey('last_login_at')) {
      context.handle(
          _lastLoginAtMeta,
          lastLoginAt.isAcceptableOrUnknown(
              data['last_login_at']!, _lastLoginAtMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AdminUserData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AdminUserData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email'])!,
      role: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}role'])!,
      permissions: $AdminUsersTable.$converterpermissions.fromSql(
          attachedDatabase.typeMapping.read(
              DriftSqlType.string, data['${effectivePrefix}permissions'])!),
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
      lastLoginAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_login_at']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $AdminUsersTable createAlias(String alias) {
    return $AdminUsersTable(attachedDatabase, alias);
  }

  static TypeConverter<List<String>, String> $converterpermissions =
      const StringListConverter();
}

class AdminUserData extends DataClass implements Insertable<AdminUserData> {
  final String id;
  final String email;
  final String role;
  final List<String> permissions;
  final bool isActive;
  final DateTime? lastLoginAt;
  final DateTime createdAt;
  const AdminUserData(
      {required this.id,
      required this.email,
      required this.role,
      required this.permissions,
      required this.isActive,
      this.lastLoginAt,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['email'] = Variable<String>(email);
    map['role'] = Variable<String>(role);
    {
      map['permissions'] = Variable<String>(
          $AdminUsersTable.$converterpermissions.toSql(permissions));
    }
    map['is_active'] = Variable<bool>(isActive);
    if (!nullToAbsent || lastLoginAt != null) {
      map['last_login_at'] = Variable<DateTime>(lastLoginAt);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  AdminUserDataCompanion toCompanion(bool nullToAbsent) {
    return AdminUserDataCompanion(
      id: Value(id),
      email: Value(email),
      role: Value(role),
      permissions: Value(permissions),
      isActive: Value(isActive),
      lastLoginAt: lastLoginAt == null && nullToAbsent
          ? const Value.absent()
          : Value(lastLoginAt),
      createdAt: Value(createdAt),
    );
  }

  factory AdminUserData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AdminUserData(
      id: serializer.fromJson<String>(json['id']),
      email: serializer.fromJson<String>(json['email']),
      role: serializer.fromJson<String>(json['role']),
      permissions: serializer.fromJson<List<String>>(json['permissions']),
      isActive: serializer.fromJson<bool>(json['isActive']),
      lastLoginAt: serializer.fromJson<DateTime?>(json['lastLoginAt']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'email': serializer.toJson<String>(email),
      'role': serializer.toJson<String>(role),
      'permissions': serializer.toJson<List<String>>(permissions),
      'isActive': serializer.toJson<bool>(isActive),
      'lastLoginAt': serializer.toJson<DateTime?>(lastLoginAt),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  AdminUserData copyWith(
          {String? id,
          String? email,
          String? role,
          List<String>? permissions,
          bool? isActive,
          Value<DateTime?> lastLoginAt = const Value.absent(),
          DateTime? createdAt}) =>
      AdminUserData(
        id: id ?? this.id,
        email: email ?? this.email,
        role: role ?? this.role,
        permissions: permissions ?? this.permissions,
        isActive: isActive ?? this.isActive,
        lastLoginAt: lastLoginAt.present ? lastLoginAt.value : this.lastLoginAt,
        createdAt: createdAt ?? this.createdAt,
      );
  AdminUserData copyWithCompanion(AdminUserDataCompanion data) {
    return AdminUserData(
      id: data.id.present ? data.id.value : this.id,
      email: data.email.present ? data.email.value : this.email,
      role: data.role.present ? data.role.value : this.role,
      permissions:
          data.permissions.present ? data.permissions.value : this.permissions,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
      lastLoginAt:
          data.lastLoginAt.present ? data.lastLoginAt.value : this.lastLoginAt,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AdminUserData(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('role: $role, ')
          ..write('permissions: $permissions, ')
          ..write('isActive: $isActive, ')
          ..write('lastLoginAt: $lastLoginAt, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, email, role, permissions, isActive, lastLoginAt, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AdminUserData &&
          other.id == this.id &&
          other.email == this.email &&
          other.role == this.role &&
          other.permissions == this.permissions &&
          other.isActive == this.isActive &&
          other.lastLoginAt == this.lastLoginAt &&
          other.createdAt == this.createdAt);
}

class AdminUserDataCompanion extends UpdateCompanion<AdminUserData> {
  final Value<String> id;
  final Value<String> email;
  final Value<String> role;
  final Value<List<String>> permissions;
  final Value<bool> isActive;
  final Value<DateTime?> lastLoginAt;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const AdminUserDataCompanion({
    this.id = const Value.absent(),
    this.email = const Value.absent(),
    this.role = const Value.absent(),
    this.permissions = const Value.absent(),
    this.isActive = const Value.absent(),
    this.lastLoginAt = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AdminUserDataCompanion.insert({
    required String id,
    required String email,
    required String role,
    required List<String> permissions,
    this.isActive = const Value.absent(),
    this.lastLoginAt = const Value.absent(),
    required DateTime createdAt,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        email = Value(email),
        role = Value(role),
        permissions = Value(permissions),
        createdAt = Value(createdAt);
  static Insertable<AdminUserData> custom({
    Expression<String>? id,
    Expression<String>? email,
    Expression<String>? role,
    Expression<String>? permissions,
    Expression<bool>? isActive,
    Expression<DateTime>? lastLoginAt,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (email != null) 'email': email,
      if (role != null) 'role': role,
      if (permissions != null) 'permissions': permissions,
      if (isActive != null) 'is_active': isActive,
      if (lastLoginAt != null) 'last_login_at': lastLoginAt,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AdminUserDataCompanion copyWith(
      {Value<String>? id,
      Value<String>? email,
      Value<String>? role,
      Value<List<String>>? permissions,
      Value<bool>? isActive,
      Value<DateTime?>? lastLoginAt,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return AdminUserDataCompanion(
      id: id ?? this.id,
      email: email ?? this.email,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (role.present) {
      map['role'] = Variable<String>(role.value);
    }
    if (permissions.present) {
      map['permissions'] = Variable<String>(
          $AdminUsersTable.$converterpermissions.toSql(permissions.value));
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (lastLoginAt.present) {
      map['last_login_at'] = Variable<DateTime>(lastLoginAt.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AdminUserDataCompanion(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('role: $role, ')
          ..write('permissions: $permissions, ')
          ..write('isActive: $isActive, ')
          ..write('lastLoginAt: $lastLoginAt, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SystemMetricsTable extends SystemMetrics
    with TableInfo<$SystemMetricsTable, SystemMetricsData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SystemMetricsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _metricTypeMeta =
      const VerificationMeta('metricType');
  @override
  late final GeneratedColumn<String> metricType = GeneratedColumn<String>(
      'metric_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<double> value = GeneratedColumn<double>(
      'value', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<Map<String, dynamic>, String>
      metadata = GeneratedColumn<String>('metadata', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<Map<String, dynamic>>(
              $SystemMetricsTable.$convertermetadata);
  static const VerificationMeta _recordedAtMeta =
      const VerificationMeta('recordedAt');
  @override
  late final GeneratedColumn<DateTime> recordedAt = GeneratedColumn<DateTime>(
      'recorded_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns =>
      [id, metricType, value, metadata, recordedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'system_metrics';
  @override
  VerificationContext validateIntegrity(Insertable<SystemMetricsData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('metric_type')) {
      context.handle(
          _metricTypeMeta,
          metricType.isAcceptableOrUnknown(
              data['metric_type']!, _metricTypeMeta));
    } else if (isInserting) {
      context.missing(_metricTypeMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('recorded_at')) {
      context.handle(
          _recordedAtMeta,
          recordedAt.isAcceptableOrUnknown(
              data['recorded_at']!, _recordedAtMeta));
    } else if (isInserting) {
      context.missing(_recordedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SystemMetricsData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SystemMetricsData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      metricType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}metric_type'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}value'])!,
      metadata: $SystemMetricsTable.$convertermetadata.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}metadata'])!),
      recordedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}recorded_at'])!,
    );
  }

  @override
  $SystemMetricsTable createAlias(String alias) {
    return $SystemMetricsTable(attachedDatabase, alias);
  }

  static TypeConverter<Map<String, dynamic>, String> $convertermetadata =
      const JsonConverter();
}

class SystemMetricsData extends DataClass
    implements Insertable<SystemMetricsData> {
  final String id;
  final String metricType;
  final double value;
  final Map<String, dynamic> metadata;
  final DateTime recordedAt;
  const SystemMetricsData(
      {required this.id,
      required this.metricType,
      required this.value,
      required this.metadata,
      required this.recordedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['metric_type'] = Variable<String>(metricType);
    map['value'] = Variable<double>(value);
    {
      map['metadata'] = Variable<String>(
          $SystemMetricsTable.$convertermetadata.toSql(metadata));
    }
    map['recorded_at'] = Variable<DateTime>(recordedAt);
    return map;
  }

  SystemMetricsDataCompanion toCompanion(bool nullToAbsent) {
    return SystemMetricsDataCompanion(
      id: Value(id),
      metricType: Value(metricType),
      value: Value(value),
      metadata: Value(metadata),
      recordedAt: Value(recordedAt),
    );
  }

  factory SystemMetricsData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SystemMetricsData(
      id: serializer.fromJson<String>(json['id']),
      metricType: serializer.fromJson<String>(json['metricType']),
      value: serializer.fromJson<double>(json['value']),
      metadata: serializer.fromJson<Map<String, dynamic>>(json['metadata']),
      recordedAt: serializer.fromJson<DateTime>(json['recordedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'metricType': serializer.toJson<String>(metricType),
      'value': serializer.toJson<double>(value),
      'metadata': serializer.toJson<Map<String, dynamic>>(metadata),
      'recordedAt': serializer.toJson<DateTime>(recordedAt),
    };
  }

  SystemMetricsData copyWith(
          {String? id,
          String? metricType,
          double? value,
          Map<String, dynamic>? metadata,
          DateTime? recordedAt}) =>
      SystemMetricsData(
        id: id ?? this.id,
        metricType: metricType ?? this.metricType,
        value: value ?? this.value,
        metadata: metadata ?? this.metadata,
        recordedAt: recordedAt ?? this.recordedAt,
      );
  SystemMetricsData copyWithCompanion(SystemMetricsDataCompanion data) {
    return SystemMetricsData(
      id: data.id.present ? data.id.value : this.id,
      metricType:
          data.metricType.present ? data.metricType.value : this.metricType,
      value: data.value.present ? data.value.value : this.value,
      metadata: data.metadata.present ? data.metadata.value : this.metadata,
      recordedAt:
          data.recordedAt.present ? data.recordedAt.value : this.recordedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SystemMetricsData(')
          ..write('id: $id, ')
          ..write('metricType: $metricType, ')
          ..write('value: $value, ')
          ..write('metadata: $metadata, ')
          ..write('recordedAt: $recordedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, metricType, value, metadata, recordedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SystemMetricsData &&
          other.id == this.id &&
          other.metricType == this.metricType &&
          other.value == this.value &&
          other.metadata == this.metadata &&
          other.recordedAt == this.recordedAt);
}

class SystemMetricsDataCompanion extends UpdateCompanion<SystemMetricsData> {
  final Value<String> id;
  final Value<String> metricType;
  final Value<double> value;
  final Value<Map<String, dynamic>> metadata;
  final Value<DateTime> recordedAt;
  final Value<int> rowid;
  const SystemMetricsDataCompanion({
    this.id = const Value.absent(),
    this.metricType = const Value.absent(),
    this.value = const Value.absent(),
    this.metadata = const Value.absent(),
    this.recordedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  SystemMetricsDataCompanion.insert({
    required String id,
    required String metricType,
    required double value,
    required Map<String, dynamic> metadata,
    required DateTime recordedAt,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        metricType = Value(metricType),
        value = Value(value),
        metadata = Value(metadata),
        recordedAt = Value(recordedAt);
  static Insertable<SystemMetricsData> custom({
    Expression<String>? id,
    Expression<String>? metricType,
    Expression<double>? value,
    Expression<String>? metadata,
    Expression<DateTime>? recordedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (metricType != null) 'metric_type': metricType,
      if (value != null) 'value': value,
      if (metadata != null) 'metadata': metadata,
      if (recordedAt != null) 'recorded_at': recordedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SystemMetricsDataCompanion copyWith(
      {Value<String>? id,
      Value<String>? metricType,
      Value<double>? value,
      Value<Map<String, dynamic>>? metadata,
      Value<DateTime>? recordedAt,
      Value<int>? rowid}) {
    return SystemMetricsDataCompanion(
      id: id ?? this.id,
      metricType: metricType ?? this.metricType,
      value: value ?? this.value,
      metadata: metadata ?? this.metadata,
      recordedAt: recordedAt ?? this.recordedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (metricType.present) {
      map['metric_type'] = Variable<String>(metricType.value);
    }
    if (value.present) {
      map['value'] = Variable<double>(value.value);
    }
    if (metadata.present) {
      map['metadata'] = Variable<String>(
          $SystemMetricsTable.$convertermetadata.toSql(metadata.value));
    }
    if (recordedAt.present) {
      map['recorded_at'] = Variable<DateTime>(recordedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SystemMetricsDataCompanion(')
          ..write('id: $id, ')
          ..write('metricType: $metricType, ')
          ..write('value: $value, ')
          ..write('metadata: $metadata, ')
          ..write('recordedAt: $recordedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AdminDatabase extends GeneratedDatabase {
  _$AdminDatabase(QueryExecutor e) : super(e);
  _$AdminDatabase.connect(DatabaseConnection c) : super.connect(c);
  $AdminDatabaseManager get managers => $AdminDatabaseManager(this);
  late final $JourneyContentTableTable journeyContentTable =
      $JourneyContentTableTable(this);
  late final $AdminUsersTable adminUsers = $AdminUsersTable(this);
  late final $SystemMetricsTable systemMetrics = $SystemMetricsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [journeyContentTable, adminUsers, systemMetrics];
}

typedef $$JourneyContentTableTableCreateCompanionBuilder
    = JourneyContentDataCompanion Function({
  required String id,
  required String title,
  required String description,
  required String category,
  required String difficulty,
  required List<String> prompts,
  Value<bool> isPublished,
  required DateTime createdAt,
  required DateTime updatedAt,
  Value<int> rowid,
});
typedef $$JourneyContentTableTableUpdateCompanionBuilder
    = JourneyContentDataCompanion Function({
  Value<String> id,
  Value<String> title,
  Value<String> description,
  Value<String> category,
  Value<String> difficulty,
  Value<List<String>> prompts,
  Value<bool> isPublished,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$JourneyContentTableTableFilterComposer
    extends Composer<_$AdminDatabase, $JourneyContentTableTable> {
  $$JourneyContentTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get difficulty => $composableBuilder(
      column: $table.difficulty, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<List<String>, List<String>, String>
      get prompts => $composableBuilder(
          column: $table.prompts,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<bool> get isPublished => $composableBuilder(
      column: $table.isPublished, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$JourneyContentTableTableOrderingComposer
    extends Composer<_$AdminDatabase, $JourneyContentTableTable> {
  $$JourneyContentTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get difficulty => $composableBuilder(
      column: $table.difficulty, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get prompts => $composableBuilder(
      column: $table.prompts, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isPublished => $composableBuilder(
      column: $table.isPublished, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$JourneyContentTableTableAnnotationComposer
    extends Composer<_$AdminDatabase, $JourneyContentTableTable> {
  $$JourneyContentTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<String> get category =>
      $composableBuilder(column: $table.category, builder: (column) => column);

  GeneratedColumn<String> get difficulty => $composableBuilder(
      column: $table.difficulty, builder: (column) => column);

  GeneratedColumnWithTypeConverter<List<String>, String> get prompts =>
      $composableBuilder(column: $table.prompts, builder: (column) => column);

  GeneratedColumn<bool> get isPublished => $composableBuilder(
      column: $table.isPublished, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$JourneyContentTableTableTableManager extends RootTableManager<
    _$AdminDatabase,
    $JourneyContentTableTable,
    JourneyContentData,
    $$JourneyContentTableTableFilterComposer,
    $$JourneyContentTableTableOrderingComposer,
    $$JourneyContentTableTableAnnotationComposer,
    $$JourneyContentTableTableCreateCompanionBuilder,
    $$JourneyContentTableTableUpdateCompanionBuilder,
    (
      JourneyContentData,
      BaseReferences<_$AdminDatabase, $JourneyContentTableTable,
          JourneyContentData>
    ),
    JourneyContentData,
    PrefetchHooks Function()> {
  $$JourneyContentTableTableTableManager(
      _$AdminDatabase db, $JourneyContentTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$JourneyContentTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$JourneyContentTableTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$JourneyContentTableTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<String> description = const Value.absent(),
            Value<String> category = const Value.absent(),
            Value<String> difficulty = const Value.absent(),
            Value<List<String>> prompts = const Value.absent(),
            Value<bool> isPublished = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              JourneyContentDataCompanion(
            id: id,
            title: title,
            description: description,
            category: category,
            difficulty: difficulty,
            prompts: prompts,
            isPublished: isPublished,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String title,
            required String description,
            required String category,
            required String difficulty,
            required List<String> prompts,
            Value<bool> isPublished = const Value.absent(),
            required DateTime createdAt,
            required DateTime updatedAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              JourneyContentDataCompanion.insert(
            id: id,
            title: title,
            description: description,
            category: category,
            difficulty: difficulty,
            prompts: prompts,
            isPublished: isPublished,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$JourneyContentTableTableProcessedTableManager = ProcessedTableManager<
    _$AdminDatabase,
    $JourneyContentTableTable,
    JourneyContentData,
    $$JourneyContentTableTableFilterComposer,
    $$JourneyContentTableTableOrderingComposer,
    $$JourneyContentTableTableAnnotationComposer,
    $$JourneyContentTableTableCreateCompanionBuilder,
    $$JourneyContentTableTableUpdateCompanionBuilder,
    (
      JourneyContentData,
      BaseReferences<_$AdminDatabase, $JourneyContentTableTable,
          JourneyContentData>
    ),
    JourneyContentData,
    PrefetchHooks Function()>;
typedef $$AdminUsersTableCreateCompanionBuilder = AdminUserDataCompanion
    Function({
  required String id,
  required String email,
  required String role,
  required List<String> permissions,
  Value<bool> isActive,
  Value<DateTime?> lastLoginAt,
  required DateTime createdAt,
  Value<int> rowid,
});
typedef $$AdminUsersTableUpdateCompanionBuilder = AdminUserDataCompanion
    Function({
  Value<String> id,
  Value<String> email,
  Value<String> role,
  Value<List<String>> permissions,
  Value<bool> isActive,
  Value<DateTime?> lastLoginAt,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

class $$AdminUsersTableFilterComposer
    extends Composer<_$AdminDatabase, $AdminUsersTable> {
  $$AdminUsersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get role => $composableBuilder(
      column: $table.role, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<List<String>, List<String>, String>
      get permissions => $composableBuilder(
          column: $table.permissions,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastLoginAt => $composableBuilder(
      column: $table.lastLoginAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$AdminUsersTableOrderingComposer
    extends Composer<_$AdminDatabase, $AdminUsersTable> {
  $$AdminUsersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get role => $composableBuilder(
      column: $table.role, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get permissions => $composableBuilder(
      column: $table.permissions, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastLoginAt => $composableBuilder(
      column: $table.lastLoginAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$AdminUsersTableAnnotationComposer
    extends Composer<_$AdminDatabase, $AdminUsersTable> {
  $$AdminUsersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<String> get role =>
      $composableBuilder(column: $table.role, builder: (column) => column);

  GeneratedColumnWithTypeConverter<List<String>, String> get permissions =>
      $composableBuilder(
          column: $table.permissions, builder: (column) => column);

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);

  GeneratedColumn<DateTime> get lastLoginAt => $composableBuilder(
      column: $table.lastLoginAt, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$AdminUsersTableTableManager extends RootTableManager<
    _$AdminDatabase,
    $AdminUsersTable,
    AdminUserData,
    $$AdminUsersTableFilterComposer,
    $$AdminUsersTableOrderingComposer,
    $$AdminUsersTableAnnotationComposer,
    $$AdminUsersTableCreateCompanionBuilder,
    $$AdminUsersTableUpdateCompanionBuilder,
    (
      AdminUserData,
      BaseReferences<_$AdminDatabase, $AdminUsersTable, AdminUserData>
    ),
    AdminUserData,
    PrefetchHooks Function()> {
  $$AdminUsersTableTableManager(_$AdminDatabase db, $AdminUsersTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AdminUsersTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AdminUsersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AdminUsersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> email = const Value.absent(),
            Value<String> role = const Value.absent(),
            Value<List<String>> permissions = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<DateTime?> lastLoginAt = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AdminUserDataCompanion(
            id: id,
            email: email,
            role: role,
            permissions: permissions,
            isActive: isActive,
            lastLoginAt: lastLoginAt,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String email,
            required String role,
            required List<String> permissions,
            Value<bool> isActive = const Value.absent(),
            Value<DateTime?> lastLoginAt = const Value.absent(),
            required DateTime createdAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              AdminUserDataCompanion.insert(
            id: id,
            email: email,
            role: role,
            permissions: permissions,
            isActive: isActive,
            lastLoginAt: lastLoginAt,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AdminUsersTableProcessedTableManager = ProcessedTableManager<
    _$AdminDatabase,
    $AdminUsersTable,
    AdminUserData,
    $$AdminUsersTableFilterComposer,
    $$AdminUsersTableOrderingComposer,
    $$AdminUsersTableAnnotationComposer,
    $$AdminUsersTableCreateCompanionBuilder,
    $$AdminUsersTableUpdateCompanionBuilder,
    (
      AdminUserData,
      BaseReferences<_$AdminDatabase, $AdminUsersTable, AdminUserData>
    ),
    AdminUserData,
    PrefetchHooks Function()>;
typedef $$SystemMetricsTableCreateCompanionBuilder = SystemMetricsDataCompanion
    Function({
  required String id,
  required String metricType,
  required double value,
  required Map<String, dynamic> metadata,
  required DateTime recordedAt,
  Value<int> rowid,
});
typedef $$SystemMetricsTableUpdateCompanionBuilder = SystemMetricsDataCompanion
    Function({
  Value<String> id,
  Value<String> metricType,
  Value<double> value,
  Value<Map<String, dynamic>> metadata,
  Value<DateTime> recordedAt,
  Value<int> rowid,
});

class $$SystemMetricsTableFilterComposer
    extends Composer<_$AdminDatabase, $SystemMetricsTable> {
  $$SystemMetricsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get metricType => $composableBuilder(
      column: $table.metricType, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<Map<String, dynamic>, Map<String, dynamic>,
          String>
      get metadata => $composableBuilder(
          column: $table.metadata,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get recordedAt => $composableBuilder(
      column: $table.recordedAt, builder: (column) => ColumnFilters(column));
}

class $$SystemMetricsTableOrderingComposer
    extends Composer<_$AdminDatabase, $SystemMetricsTable> {
  $$SystemMetricsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get metricType => $composableBuilder(
      column: $table.metricType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get metadata => $composableBuilder(
      column: $table.metadata, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get recordedAt => $composableBuilder(
      column: $table.recordedAt, builder: (column) => ColumnOrderings(column));
}

class $$SystemMetricsTableAnnotationComposer
    extends Composer<_$AdminDatabase, $SystemMetricsTable> {
  $$SystemMetricsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get metricType => $composableBuilder(
      column: $table.metricType, builder: (column) => column);

  GeneratedColumn<double> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Map<String, dynamic>, String> get metadata =>
      $composableBuilder(column: $table.metadata, builder: (column) => column);

  GeneratedColumn<DateTime> get recordedAt => $composableBuilder(
      column: $table.recordedAt, builder: (column) => column);
}

class $$SystemMetricsTableTableManager extends RootTableManager<
    _$AdminDatabase,
    $SystemMetricsTable,
    SystemMetricsData,
    $$SystemMetricsTableFilterComposer,
    $$SystemMetricsTableOrderingComposer,
    $$SystemMetricsTableAnnotationComposer,
    $$SystemMetricsTableCreateCompanionBuilder,
    $$SystemMetricsTableUpdateCompanionBuilder,
    (
      SystemMetricsData,
      BaseReferences<_$AdminDatabase, $SystemMetricsTable, SystemMetricsData>
    ),
    SystemMetricsData,
    PrefetchHooks Function()> {
  $$SystemMetricsTableTableManager(
      _$AdminDatabase db, $SystemMetricsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SystemMetricsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SystemMetricsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SystemMetricsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> metricType = const Value.absent(),
            Value<double> value = const Value.absent(),
            Value<Map<String, dynamic>> metadata = const Value.absent(),
            Value<DateTime> recordedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              SystemMetricsDataCompanion(
            id: id,
            metricType: metricType,
            value: value,
            metadata: metadata,
            recordedAt: recordedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String metricType,
            required double value,
            required Map<String, dynamic> metadata,
            required DateTime recordedAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              SystemMetricsDataCompanion.insert(
            id: id,
            metricType: metricType,
            value: value,
            metadata: metadata,
            recordedAt: recordedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$SystemMetricsTableProcessedTableManager = ProcessedTableManager<
    _$AdminDatabase,
    $SystemMetricsTable,
    SystemMetricsData,
    $$SystemMetricsTableFilterComposer,
    $$SystemMetricsTableOrderingComposer,
    $$SystemMetricsTableAnnotationComposer,
    $$SystemMetricsTableCreateCompanionBuilder,
    $$SystemMetricsTableUpdateCompanionBuilder,
    (
      SystemMetricsData,
      BaseReferences<_$AdminDatabase, $SystemMetricsTable, SystemMetricsData>
    ),
    SystemMetricsData,
    PrefetchHooks Function()>;

class $AdminDatabaseManager {
  final _$AdminDatabase _db;
  $AdminDatabaseManager(this._db);
  $$JourneyContentTableTableTableManager get journeyContentTable =>
      $$JourneyContentTableTableTableManager(_db, _db.journeyContentTable);
  $$AdminUsersTableTableManager get adminUsers =>
      $$AdminUsersTableTableManager(_db, _db.adminUsers);
  $$SystemMetricsTableTableManager get systemMetrics =>
      $$SystemMetricsTableTableManager(_db, _db.systemMetrics);
}
