import 'package:meditatingleo_admin/core/services/supabase_service.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';
import 'package:meditatingleo_admin/shared/models/journey_content.dart';
import 'package:meditatingleo_admin/shared/models/result.dart';
import 'package:meditatingleo_admin/features/content_management/domain/entities/journey_entity.dart';
import 'package:meditatingleo_admin/features/content_management/domain/entities/prompt_entity.dart';

/// Repository for managing journey content with admin privileges
class ContentRepository {
  final SupabaseService _supabaseService;
  final AdminDatabase _database;

  ContentRepository(this._supabaseService, this._database);

  /// Convert JourneyContent model to Supabase JSON format
  Map<String, dynamic> _toSupabaseJson(JourneyContent journeyContent) {
    return {
      'id': journeyContent.id,
      'title': journeyContent.title,
      'description': journeyContent.description,
      'category': journeyContent.category,
      'difficulty': journeyContent.difficulty,
      'prompts': journeyContent.prompts,
      'is_published': journeyContent.isPublished,
      'created_at': journeyContent.createdAt.toIso8601String(),
      'updated_at': journeyContent.updatedAt.toIso8601String(),
      if (journeyContent.imageUrl != null) 'image_url': journeyContent.imageUrl,
      if (journeyContent.estimatedDuration != null)
        'estimated_duration': journeyContent.estimatedDuration,
      if (journeyContent.tags != null) 'tags': journeyContent.tags,
      if (journeyContent.metadata != null) 'metadata': journeyContent.metadata,
    };
  }

  /// Create new journey content
  Future<Result<JourneyContent, AppError>> createJourneyContent(
    JourneyContent journeyContent,
  ) async {
    try {
      // Insert into Supabase
      final result = await _supabaseService.insert(
        'journey_content',
        _toSupabaseJson(journeyContent),
      );

      return result.mapData((data) {
        if (data.isEmpty) {
          throw Exception('No data returned from insert operation');
        }
        return JourneyContent.fromJson(data.first);
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to create journey content: $e'));
    }
  }

  /// Get all journey content
  Future<Result<List<JourneyContent>, AppError>> getAllJourneyContent() async {
    try {
      final result = await _supabaseService.query('journey_content');

      return result.mapData((data) {
        return data.map((json) => JourneyContent.fromJson(json)).toList();
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to fetch journey content: $e'));
    }
  }

  /// Get journey content by ID
  Future<Result<JourneyContent, AppError>> getJourneyContentById(
      String id) async {
    try {
      final result = await _supabaseService.queryById('journey_content', id);

      return result.flatMap((data) {
        if (data.isEmpty) {
          return Result.failure(AppError.notFound('Journey content not found'));
        }
        return Result.success(JourneyContent.fromJson(data.first));
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to fetch journey content: $e'));
    }
  }

  /// Update journey content
  Future<Result<JourneyContent, AppError>> updateJourneyContent(
    JourneyContent journeyContent,
  ) async {
    try {
      final updateData = _toSupabaseJson(journeyContent);
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final result = await _supabaseService.update(
        'journey_content',
        journeyContent.id,
        updateData,
      );

      return result.flatMap((data) {
        if (data.isEmpty) {
          return Result.failure(AppError.notFound('Journey content not found'));
        }
        return Result.success(JourneyContent.fromJson(data.first));
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to update journey content: $e'));
    }
  }

  /// Delete journey content
  Future<Result<void, AppError>> deleteJourneyContent(String id) async {
    try {
      return await _supabaseService.delete('journey_content', id);
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to delete journey content: $e'));
    }
  }

  /// Publish journey content
  Future<Result<JourneyContent, AppError>> publishJourneyContent(
      String id) async {
    try {
      final result = await _supabaseService.update(
        'journey_content',
        id,
        {
          'is_published': true,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      return result.flatMap((data) {
        if (data.isEmpty) {
          return Result.failure(AppError.notFound('Journey content not found'));
        }
        return Result.success(JourneyContent.fromJson(data.first));
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to publish journey content: $e'));
    }
  }

  /// Unpublish journey content
  Future<Result<JourneyContent, AppError>> unpublishJourneyContent(
      String id) async {
    try {
      final result = await _supabaseService.update(
        'journey_content',
        id,
        {
          'is_published': false,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      return result.flatMap((data) {
        if (data.isEmpty) {
          return Result.failure(AppError.notFound('Journey content not found'));
        }
        return Result.success(JourneyContent.fromJson(data.first));
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to unpublish journey content: $e'));
    }
  }

  /// Get journey content by category
  Future<Result<List<JourneyContent>, AppError>> getJourneyContentByCategory(
    String category,
  ) async {
    try {
      // For now, we'll filter client-side. In production, this should be a server-side filter
      final allContentResult = await getAllJourneyContent();

      return allContentResult.mapData((journeys) {
        return journeys
            .where((journey) => journey.category == category)
            .toList();
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to fetch journey content by category: $e'));
    }
  }

  /// Get published journey content only
  Future<Result<List<JourneyContent>, AppError>>
      getPublishedJourneyContent() async {
    try {
      // For now, we'll filter client-side. In production, this should be a server-side filter
      final allContentResult = await getAllJourneyContent();

      return allContentResult.mapData((journeys) {
        return journeys.where((journey) => journey.isPublished).toList();
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to fetch published journey content: $e'));
    }
  }

  /// Get draft journey content only
  Future<Result<List<JourneyContent>, AppError>>
      getDraftJourneyContent() async {
    try {
      // For now, we'll filter client-side. In production, this should be a server-side filter
      final allContentResult = await getAllJourneyContent();

      return allContentResult.mapData((journeys) {
        return journeys.where((journey) => !journey.isPublished).toList();
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to fetch draft journey content: $e'));
    }
  }

  /// Get content analytics
  Future<Result<Map<String, dynamic>, AppError>> getContentAnalytics() async {
    try {
      final result = await _supabaseService.rpc('get_content_analytics', {});

      return result.mapData((data) {
        if (data is Map<String, dynamic>) {
          return data;
        }
        // If RPC doesn't exist, calculate basic analytics client-side
        return _calculateBasicAnalytics();
      });
    } catch (e) {
      // Fallback to basic analytics if RPC fails
      return Result.success(_calculateBasicAnalytics());
    }
  }

  /// Calculate basic analytics client-side (fallback)
  Map<String, dynamic> _calculateBasicAnalytics() {
    // This is a placeholder. In a real implementation, you'd fetch the data
    // and calculate these metrics
    return {
      'total_journeys': 0,
      'published_journeys': 0,
      'draft_journeys': 0,
      'categories': <String, int>{},
      'difficulty_distribution': <String, int>{},
      'last_updated': DateTime.now().toIso8601String(),
    };
  }

  /// Search journey content
  Future<Result<List<JourneyContent>, AppError>> searchJourneyContent(
    String query,
  ) async {
    try {
      // For now, we'll search client-side. In production, this should use full-text search
      final allContentResult = await getAllJourneyContent();

      return allContentResult.mapData((journeys) {
        final lowercaseQuery = query.toLowerCase();
        return journeys.where((journey) {
          return journey.title.toLowerCase().contains(lowercaseQuery) ||
              journey.description.toLowerCase().contains(lowercaseQuery) ||
              journey.category.toLowerCase().contains(lowercaseQuery) ||
              journey.prompts.any(
                  (prompt) => prompt.toLowerCase().contains(lowercaseQuery));
        }).toList();
      });
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to search journey content: $e'));
    }
  }

  /// Bulk update journey content
  Future<Result<List<JourneyContent>, AppError>> bulkUpdateJourneyContent(
    List<String> ids,
    Map<String, dynamic> updates,
  ) async {
    try {
      final results = <JourneyContent>[];

      for (final id in ids) {
        final updateResult = await _supabaseService.update(
          'journey_content',
          id,
          {
            ...updates,
            'updated_at': DateTime.now().toIso8601String(),
          },
        );

        if (updateResult.isSuccess && updateResult.data!.isNotEmpty) {
          results.add(JourneyContent.fromJson(updateResult.data!.first));
        }
      }

      return Result.success(results);
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to bulk update journey content: $e'));
    }
  }

  /// Bulk delete journey content
  Future<Result<void, AppError>> bulkDeleteJourneyContent(
      List<String> ids) async {
    try {
      for (final id in ids) {
        await _supabaseService.delete('journey_content', id);
      }

      return Result.success(null);
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to bulk delete journey content: $e'));
    }
  }

  // ===== METHODS FOR CONTENT MANAGEMENT PROVIDERS =====
  // These methods provide a simpler interface for the Riverpod providers
  // and convert between JourneyEntity and JourneyContent models

  /// Convert JourneyEntity to JourneyContent for database operations
  JourneyContent _journeyEntityToContent(JourneyEntity entity) {
    return JourneyContent(
      id: entity.id,
      title: entity.title,
      description: entity.description,
      category: entity.category ?? 'general',
      difficulty: entity.difficulty,
      prompts: entity.prompts.map((p) => p.text).toList(),
      isPublished: entity.isPublished,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      estimatedDuration: entity.estimatedDuration,
      tags: entity.tags,
    );
  }

  /// Convert JourneyContent to JourneyEntity for provider consumption
  JourneyEntity _journeyContentToEntity(JourneyContent content) {
    return JourneyEntity(
      id: content.id,
      title: content.title,
      description: content.description,
      prompts: content.prompts.asMap().entries.map((entry) {
        return PromptEntity(
          id: '${content.id}_prompt_${entry.key}',
          text: entry.value,
          order: entry.key,
          createdAt: content.createdAt,
          updatedAt: content.updatedAt,
        );
      }).toList(),
      category: content.category,
      difficulty: content.difficulty,
      estimatedDuration: content.estimatedDuration ?? 15,
      isPublished: content.isPublished,
      tags: content.tags ?? [],
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
    );
  }

  /// Get all journeys as JourneyEntity objects
  Future<List<JourneyEntity>> getAllJourneys() async {
    final result = await getAllJourneyContent();
    if (result.isSuccess) {
      return result.data!.map(_journeyContentToEntity).toList();
    } else {
      throw Exception(result.error!.message);
    }
  }

  /// Create a new journey from JourneyEntity
  Future<JourneyEntity> createJourney(JourneyEntity journey) async {
    final content = _journeyEntityToContent(journey);
    final result = await createJourneyContent(content);
    if (result.isSuccess) {
      return _journeyContentToEntity(result.data!);
    } else {
      throw Exception(result.error!.message);
    }
  }

  /// Update an existing journey from JourneyEntity
  Future<JourneyEntity> updateJourney(JourneyEntity journey) async {
    final content = _journeyEntityToContent(journey);
    final result = await updateJourneyContent(content);
    if (result.isSuccess) {
      return _journeyContentToEntity(result.data!);
    } else {
      throw Exception(result.error!.message);
    }
  }

  /// Delete a journey by ID
  Future<void> deleteJourney(String journeyId) async {
    final result = await deleteJourneyContent(journeyId);
    if (!result.isSuccess) {
      throw Exception(result.error!.message);
    }
  }
}
