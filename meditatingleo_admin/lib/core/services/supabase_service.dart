import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/shared/models/result.dart';

/// Admin-specific Supabase service with elevated permissions
class SupabaseService {
  final SupabaseClient _client;

  SupabaseService(this._client);

  /// Get the underlying Supabase client for advanced operations
  SupabaseClient get client => _client;

  /// Get current authenticated user
  User? getCurrentUser() {
    return _client.auth.currentUser;
  }

  /// Sign in with email and password for admin users
  Future<Result<AuthResponse, AppError>> signInWithEmail(
    String email,
    String password,
  ) async {
    try {
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return Result.success(response);
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Sign out current admin user
  Future<Result<void, AppError>> signOut() async {
    try {
      await _client.auth.signOut();
      return Result.success(null);
    } on AuthException catch (e) {
      return Result.failure(AppError.authentication(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Generic database query operation
  Future<Result<List<Map<String, dynamic>>, AppError>> query(
      String tableName) async {
    try {
      final response = await _client.from(tableName).select();
      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Query by ID
  Future<Result<List<Map<String, dynamic>>, AppError>> queryById(
    String tableName,
    String id,
  ) async {
    try {
      final response = await _client.from(tableName).select().eq('id', id);
      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Insert data into table
  Future<Result<List<Map<String, dynamic>>, AppError>> insert(
    String tableName,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _client.from(tableName).insert(data).select();
      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Update data in table
  Future<Result<List<Map<String, dynamic>>, AppError>> update(
    String tableName,
    String id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response =
          await _client.from(tableName).update(data).eq('id', id).select();
      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Delete data from table
  Future<Result<void, AppError>> delete(String tableName, String id) async {
    try {
      await _client.from(tableName).delete().eq('id', id);
      return Result.success(null);
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Execute RPC (Remote Procedure Call) function
  Future<Result<dynamic, AppError>> rpc(
    String functionName,
    Map<String, dynamic> params,
  ) async {
    try {
      final response = await _client.rpc(functionName, params: params);
      return Result.success(response);
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }

  /// Create real-time subscription for table changes
  Stream<Map<String, dynamic>> subscribe(String tableName) {
    final controller = StreamController<Map<String, dynamic>>();

    final subscription = _client
        .channel('admin_$tableName')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: tableName,
          callback: (payload) {
            controller.add({
              'event': payload.eventType.name,
              'table': tableName,
              'new': payload.newRecord,
              'old': payload.oldRecord,
            });
          },
        )
        .subscribe();

    // Handle subscription cleanup
    controller.onCancel = () {
      subscription.unsubscribe();
    };

    return controller.stream;
  }

  /// Get authentication state stream
  Stream<AuthState> get authStateStream {
    return _client.auth.onAuthStateChange;
  }

  /// Check if user has admin permissions
  bool hasAdminPermissions() {
    final user = getCurrentUser();
    if (user == null) return false;

    final role = user.appMetadata['role'] as String?;
    return role == 'admin' ||
        role == 'content_manager' ||
        role == 'system_admin';
  }

  /// Get user role
  String? getUserRole() {
    final user = getCurrentUser();
    return user?.appMetadata['role'] as String?;
  }

  /// Get user permissions
  List<String> getUserPermissions() {
    final user = getCurrentUser();
    if (user == null) return [];

    final permissions = user.userMetadata?['permissions'];
    if (permissions is List) {
      return List<String>.from(permissions);
    }
    return [];
  }
}
