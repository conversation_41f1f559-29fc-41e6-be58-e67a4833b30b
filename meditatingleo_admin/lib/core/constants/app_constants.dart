/// Core application constants for MeditatingLeo Admin Panel
///
/// This file contains all the constant values used throughout the admin
/// application including app metadata, configuration values, and UI constants.
///
/// Following Flutter 2025+ standards with proper organization and documentation.
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // App Metadata
  static const String adminAppName = 'MeditatingLeo Admin';
  static const String adminAppVersion = '0.1.0';
  static const String adminAppDescription =
      'Administrative interface for MeditatingLeo platform';

  // App Configuration
  static const String appPackageName = 'com.meditatingleo.admin';
  static const String appBundleId = 'com.meditatingleo.admin';

  // Environment Configuration
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  static const bool isProduction = environment == 'production';
  static const bool isDevelopment = environment == 'development';
  static const bool isStaging = environment == 'staging';

  // API Configuration
  static const String supabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: '',
  );

  static const String supabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: '',
  );

  // Admin-Specific Configuration
  static const String adminRole = 'admin';
  static const String contentManagerRole = 'content_manager';
  static const String systemAdminRole = 'system_admin';
  static const String superAdminRole = 'super_admin';

  // Database Configuration
  static const String databaseName = 'meditatingleo_admin.db';
  static const int databaseVersion = 1;

  // UI Configuration
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload Limits
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
  ];

  static const List<String> allowedDocumentTypes = [
    'pdf',
    'doc',
    'docx',
    'txt',
    'md',
  ];

  // Content Management
  static const int maxJourneyTitleLength = 100;
  static const int maxPromptLength = 1000;
  static const int maxExplanationLength = 2000;

  // Session Configuration
  static const Duration sessionTimeout = Duration(hours: 8);
  static const Duration refreshTokenThreshold = Duration(minutes: 15);

  // Error Messages
  static const String genericErrorMessage =
      'An unexpected error occurred. Please try again.';
  static const String networkErrorMessage =
      'Network error. Please check your connection.';
  static const String authErrorMessage =
      'Authentication failed. Please sign in again.';
  static const String permissionErrorMessage =
      'You do not have permission to perform this action.';

  // Success Messages
  static const String saveSuccessMessage = 'Changes saved successfully.';
  static const String deleteSuccessMessage = 'Item deleted successfully.';
  static const String createSuccessMessage = 'Item created successfully.';
  static const String updateSuccessMessage = 'Item updated successfully.';

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const String emailRegexPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';

  // Feature Flags
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );

  static const bool enableErrorReporting = bool.fromEnvironment(
    'ENABLE_ERROR_REPORTING',
    defaultValue: true,
  );

  static const bool enableDebugMode = bool.fromEnvironment(
    'ENABLE_DEBUG_MODE',
    defaultValue: false,
  );

  // Desktop-Specific Configuration
  static const double minWindowWidth = 1200.0;
  static const double minWindowHeight = 800.0;
  static const double defaultWindowWidth = 1440.0;
  static const double defaultWindowHeight = 900.0;

  // Content Types
  static const String journeyContentType = 'journey';
  static const String promptContentType = 'prompt';
  static const String explanationContentType = 'explanation';

  // Admin Panel Sections
  static const String dashboardSection = 'dashboard';
  static const String contentSection = 'content';
  static const String usersSection = 'users';
  static const String analyticsSection = 'analytics';
  static const String settingsSection = 'settings';
  static const String systemSection = 'system';

  // Routes (will be used by go_router)
  static const String dashboardRoute = '/dashboard';
  static const String loginRoute = '/login';
  static const String contentRoute = '/content';
  static const String usersRoute = '/users';
  static const String analyticsRoute = '/analytics';
  static const String settingsRoute = '/settings';
  static const String systemRoute = '/system';

  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userPreferencesKey = 'user_preferences';
  static const String themePreferenceKey = 'theme_preference';
  static const String languagePreferenceKey = 'language_preference';

  // Logging
  static const String loggerName = 'MeditatingLeoAdmin';
  static const int maxLogEntries = 1000;

  // Performance
  static const Duration cacheTimeout = Duration(minutes: 30);
  static const int maxCacheSize = 100;

  // Accessibility
  static const double minTouchTargetSize = 44.0;
  static const double defaultFontSize = 14.0;
  static const double largeFontSize = 18.0;
  static const double smallFontSize = 12.0;

  // Validation Methods
  static bool isValidEmail(String email) {
    return RegExp(emailRegexPattern).hasMatch(email);
  }

  static bool isValidPassword(String password) {
    return password.length >= minPasswordLength &&
        password.length <= maxPasswordLength;
  }

  static bool isValidFileSize(int fileSize) {
    return fileSize <= maxFileSize;
  }

  static bool isAllowedImageType(String extension) {
    return allowedImageTypes.contains(extension.toLowerCase());
  }

  static bool isAllowedDocumentType(String extension) {
    return allowedDocumentTypes.contains(extension.toLowerCase());
  }

  // Environment Validation
  static void validateEnvironment({bool skipForTests = false}) {
    // Skip validation during tests or when explicitly requested
    if (skipForTests || environment == 'test') return;

    assert(supabaseUrl.isNotEmpty, 'SUPABASE_URL must be provided');
    assert(supabaseAnonKey.isNotEmpty, 'SUPABASE_ANON_KEY must be provided');
  }
}
