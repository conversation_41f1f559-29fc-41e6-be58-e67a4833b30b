import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../core/constants/app_constants.dart';
import 'routes.dart';

part 'app_router.g.dart';

/// Router configuration provider for MeditatingLeo Admin Panel
///
/// This provider configures GoRouter with all the routes, navigation guards,
/// and error handling for the admin application.
///
/// Features:
/// - Route-based navigation
/// - Authentication guards
/// - Error handling
/// - Deep linking support
/// - Desktop-optimized navigation
@riverpod
GoRouter appRouter(Ref ref) {
  return GoRouter(
    // Initial route
    initialLocation: AppRoutes.dashboard,

    // Debug logging
    debugLogDiagnostics: AppConstants.isDevelopment,

    // Error handling
    errorBuilder: (context, state) => _ErrorPage(
      error: state.error?.toString() ?? 'Unknown error',
      path: state.matchedLocation,
    ),

    // Route configuration
    routes: [
      // Authentication Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const _PlaceholderPage(
          title: 'Login',
          message: 'Admin login page will be implemented here',
        ),
      ),

      // Main Application Shell
      ShellRoute(
        builder: (context, state, child) => _AdminShell(child: child),
        routes: [
          // Dashboard
          GoRoute(
            path: AppRoutes.dashboard,
            name: 'dashboard',
            builder: (context, state) => const _PlaceholderPage(
              title: 'Dashboard',
              message: 'Admin dashboard will be implemented here',
            ),
          ),

          // Content Management
          GoRoute(
            path: AppRoutes.content,
            name: 'content',
            builder: (context, state) => const _PlaceholderPage(
              title: 'Content Management',
              message: 'Content management interface will be implemented here',
            ),
            routes: [
              GoRoute(
                path: '/journeys',
                name: 'journeys',
                builder: (context, state) => const _PlaceholderPage(
                  title: 'Journey Management',
                  message:
                      'Journey management interface will be implemented here',
                ),
              ),
              GoRoute(
                path: '/prompts',
                name: 'prompts',
                builder: (context, state) => const _PlaceholderPage(
                  title: 'Prompt Management',
                  message:
                      'Prompt management interface will be implemented here',
                ),
              ),
            ],
          ),

          // User Management
          GoRoute(
            path: AppRoutes.users,
            name: 'users',
            builder: (context, state) => const _PlaceholderPage(
              title: 'User Management',
              message: 'User management interface will be implemented here',
            ),
          ),

          // Analytics
          GoRoute(
            path: AppRoutes.analytics,
            name: 'analytics',
            builder: (context, state) => const _PlaceholderPage(
              title: 'Analytics',
              message: 'Analytics dashboard will be implemented here',
            ),
          ),

          // Settings
          GoRoute(
            path: AppRoutes.settings,
            name: 'settings',
            builder: (context, state) => const _PlaceholderPage(
              title: 'Settings',
              message: 'Settings interface will be implemented here',
            ),
          ),

          // System Administration
          GoRoute(
            path: AppRoutes.system,
            name: 'system',
            builder: (context, state) => const _PlaceholderPage(
              title: 'System Administration',
              message:
                  'System administration interface will be implemented here',
            ),
          ),
        ],
      ),
    ],

    // Redirect logic for authentication
    redirect: (context, state) {
      // TODO: Implement authentication logic
      // For now, allow all routes
      return null;
    },
  );
}

/// Admin shell widget that provides the main layout structure
///
/// This widget provides the desktop-optimized layout with:
/// - Navigation rail
/// - App bar
/// - Main content area
/// - Responsive design
class _AdminShell extends StatelessWidget {
  final Widget child;

  const _AdminShell({required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Navigation Rail
          NavigationRail(
            selectedIndex: _getSelectedIndex(context),
            onDestinationSelected: (index) =>
                _onDestinationSelected(context, index),
            labelType: NavigationRailLabelType.all,
            destinations: const [
              NavigationRailDestination(
                icon: Icon(Icons.dashboard_outlined),
                selectedIcon: Icon(Icons.dashboard),
                label: Text('Dashboard'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.content_paste_outlined),
                selectedIcon: Icon(Icons.content_paste),
                label: Text('Content'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.people_outlined),
                selectedIcon: Icon(Icons.people),
                label: Text('Users'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.analytics_outlined),
                selectedIcon: Icon(Icons.analytics),
                label: Text('Analytics'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.settings_outlined),
                selectedIcon: Icon(Icons.settings),
                label: Text('Settings'),
              ),
              NavigationRailDestination(
                icon: Icon(Icons.admin_panel_settings_outlined),
                selectedIcon: Icon(Icons.admin_panel_settings),
                label: Text('System'),
              ),
            ],
          ),

          // Vertical Divider
          const VerticalDivider(thickness: 1, width: 1),

          // Main Content Area
          Expanded(
            child: Column(
              children: [
                // App Bar
                AppBar(
                  title: Text(_getPageTitle(context)),
                  automaticallyImplyLeading: false,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.notifications_outlined),
                      onPressed: () {
                        // TODO: Implement notifications
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.account_circle_outlined),
                      onPressed: () {
                        // TODO: Implement user menu
                      },
                    ),
                    const SizedBox(width: 8),
                  ],
                ),

                // Page Content
                Expanded(child: child),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _getSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).matchedLocation;

    if (location.startsWith(AppRoutes.dashboard)) return 0;
    if (location.startsWith(AppRoutes.content)) return 1;
    if (location.startsWith(AppRoutes.users)) return 2;
    if (location.startsWith(AppRoutes.analytics)) return 3;
    if (location.startsWith(AppRoutes.settings)) return 4;
    if (location.startsWith(AppRoutes.system)) return 5;

    return 0; // Default to dashboard
  }

  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.dashboard);
        break;
      case 1:
        context.go(AppRoutes.content);
        break;
      case 2:
        context.go(AppRoutes.users);
        break;
      case 3:
        context.go(AppRoutes.analytics);
        break;
      case 4:
        context.go(AppRoutes.settings);
        break;
      case 5:
        context.go(AppRoutes.system);
        break;
    }
  }

  String _getPageTitle(BuildContext context) {
    final location = GoRouterState.of(context).matchedLocation;

    if (location.startsWith(AppRoutes.dashboard)) return 'Dashboard';
    if (location.startsWith(AppRoutes.content)) return 'Content Management';
    if (location.startsWith(AppRoutes.users)) return 'User Management';
    if (location.startsWith(AppRoutes.analytics)) return 'Analytics';
    if (location.startsWith(AppRoutes.settings)) return 'Settings';
    if (location.startsWith(AppRoutes.system)) return 'System Administration';

    return AppConstants.adminAppName;
  }
}

/// Placeholder page widget for routes that haven't been implemented yet
class _PlaceholderPage extends StatelessWidget {
  final String title;
  final String message;

  const _PlaceholderPage({
    required this.title,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Error page widget for handling navigation errors
class _ErrorPage extends StatelessWidget {
  final String error;
  final String path;

  const _ErrorPage({
    required this.error,
    required this.path,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Page Not Found',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 16),
              Text(
                'The page at "$path" could not be found.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              FilledButton(
                onPressed: () => context.go(AppRoutes.dashboard),
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
