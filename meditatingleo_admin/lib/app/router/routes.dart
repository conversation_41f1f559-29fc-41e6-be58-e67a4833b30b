/// Route definitions for MeditatingLeo Admin Panel
/// 
/// This file contains all the route paths used throughout the admin application.
/// Centralizing routes here makes it easier to maintain and update navigation.
/// 
/// Following Flutter 2025+ standards with clear organization and documentation.
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Authentication Routes
  static const String login = '/login';
  static const String logout = '/logout';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';

  // Main Application Routes
  static const String dashboard = '/dashboard';
  static const String content = '/content';
  static const String users = '/users';
  static const String analytics = '/analytics';
  static const String settings = '/settings';
  static const String system = '/system';

  // Content Management Sub-routes
  static const String journeys = '/content/journeys';
  static const String journeyCreate = '/content/journeys/create';
  static const String journeyEdit = '/content/journeys/edit';
  static const String journeyView = '/content/journeys/view';
  
  static const String prompts = '/content/prompts';
  static const String promptCreate = '/content/prompts/create';
  static const String promptEdit = '/content/prompts/edit';
  static const String promptView = '/content/prompts/view';
  
  static const String explanations = '/content/explanations';
  static const String explanationCreate = '/content/explanations/create';
  static const String explanationEdit = '/content/explanations/edit';
  static const String explanationView = '/content/explanations/view';

  // User Management Sub-routes
  static const String usersList = '/users/list';
  static const String userView = '/users/view';
  static const String userEdit = '/users/edit';
  static const String userCreate = '/users/create';
  static const String userRoles = '/users/roles';
  static const String userPermissions = '/users/permissions';

  // Analytics Sub-routes
  static const String analyticsOverview = '/analytics/overview';
  static const String analyticsUsers = '/analytics/users';
  static const String analyticsContent = '/analytics/content';
  static const String analyticsEngagement = '/analytics/engagement';
  static const String analyticsReports = '/analytics/reports';

  // Settings Sub-routes
  static const String settingsGeneral = '/settings/general';
  static const String settingsAccount = '/settings/account';
  static const String settingsSecurity = '/settings/security';
  static const String settingsNotifications = '/settings/notifications';
  static const String settingsIntegrations = '/settings/integrations';

  // System Administration Sub-routes
  static const String systemOverview = '/system/overview';
  static const String systemLogs = '/system/logs';
  static const String systemBackup = '/system/backup';
  static const String systemMaintenance = '/system/maintenance';
  static const String systemSecurity = '/system/security';
  static const String systemAudit = '/system/audit';

  // Helper methods for route generation
  
  /// Generate journey route with ID
  static String journeyWithId(String id) => '$journeyView/$id';
  
  /// Generate journey edit route with ID
  static String journeyEditWithId(String id) => '$journeyEdit/$id';
  
  /// Generate prompt route with ID
  static String promptWithId(String id) => '$promptView/$id';
  
  /// Generate prompt edit route with ID
  static String promptEditWithId(String id) => '$promptEdit/$id';
  
  /// Generate explanation route with ID
  static String explanationWithId(String id) => '$explanationView/$id';
  
  /// Generate explanation edit route with ID
  static String explanationEditWithId(String id) => '$explanationEdit/$id';
  
  /// Generate user route with ID
  static String userWithId(String id) => '$userView/$id';
  
  /// Generate user edit route with ID
  static String userEditWithId(String id) => '$userEdit/$id';

  // Route validation helpers
  
  /// Check if route is an authentication route
  static bool isAuthRoute(String route) {
    return route == login ||
           route == logout ||
           route == forgotPassword ||
           route == resetPassword;
  }
  
  /// Check if route requires authentication
  static bool requiresAuth(String route) {
    return !isAuthRoute(route);
  }
  
  /// Check if route is a content management route
  static bool isContentRoute(String route) {
    return route.startsWith(content);
  }
  
  /// Check if route is a user management route
  static bool isUserRoute(String route) {
    return route.startsWith(users);
  }
  
  /// Check if route is an analytics route
  static bool isAnalyticsRoute(String route) {
    return route.startsWith(analytics);
  }
  
  /// Check if route is a settings route
  static bool isSettingsRoute(String route) {
    return route.startsWith(settings);
  }
  
  /// Check if route is a system administration route
  static bool isSystemRoute(String route) {
    return route.startsWith(system);
  }

  // Route metadata for navigation
  
  /// Get display name for route
  static String getDisplayName(String route) {
    switch (route) {
      case dashboard:
        return 'Dashboard';
      case content:
        return 'Content Management';
      case journeys:
        return 'Journeys';
      case prompts:
        return 'Prompts';
      case explanations:
        return 'Explanations';
      case users:
        return 'User Management';
      case analytics:
        return 'Analytics';
      case settings:
        return 'Settings';
      case system:
        return 'System Administration';
      case login:
        return 'Login';
      default:
        return 'Unknown';
    }
  }
  
  /// Get icon for route
  static String getIconName(String route) {
    switch (route) {
      case dashboard:
        return 'dashboard';
      case content:
        return 'content_paste';
      case journeys:
        return 'map';
      case prompts:
        return 'quiz';
      case explanations:
        return 'help';
      case users:
        return 'people';
      case analytics:
        return 'analytics';
      case settings:
        return 'settings';
      case system:
        return 'admin_panel_settings';
      case login:
        return 'login';
      default:
        return 'help';
    }
  }
  
  /// Get breadcrumb path for route
  static List<String> getBreadcrumb(String route) {
    final segments = route.split('/').where((s) => s.isNotEmpty).toList();
    final breadcrumb = <String>[];
    
    for (int i = 0; i < segments.length; i++) {
      final path = '/${segments.sublist(0, i + 1).join('/')}';
      breadcrumb.add(path);
    }
    
    return breadcrumb;
  }
  
  /// Get parent route
  static String? getParentRoute(String route) {
    final segments = route.split('/').where((s) => s.isNotEmpty).toList();
    
    if (segments.length <= 1) {
      return null;
    }
    
    return '/${segments.sublist(0, segments.length - 1).join('/')}';
  }
  
  /// Check if route has children
  static bool hasChildren(String route) {
    switch (route) {
      case content:
      case users:
      case analytics:
      case settings:
      case system:
        return true;
      default:
        return false;
    }
  }
  
  /// Get child routes
  static List<String> getChildRoutes(String route) {
    switch (route) {
      case content:
        return [journeys, prompts, explanations];
      case users:
        return [usersList, userRoles, userPermissions];
      case analytics:
        return [analyticsOverview, analyticsUsers, analyticsContent, analyticsEngagement, analyticsReports];
      case settings:
        return [settingsGeneral, settingsAccount, settingsSecurity, settingsNotifications, settingsIntegrations];
      case system:
        return [systemOverview, systemLogs, systemBackup, systemMaintenance, systemSecurity, systemAudit];
      default:
        return [];
    }
  }
}
