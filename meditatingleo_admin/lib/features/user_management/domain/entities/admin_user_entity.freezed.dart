// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_user_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdminUserEntity _$AdminUserEntityFromJson(Map<String, dynamic> json) {
  return _AdminUserEntity.fromJson(json);
}

/// @nodoc
mixin _$AdminUserEntity {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get role => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  String? get profileImageUrl => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  int get loginCount => throw _privateConstructorUsedError;
  int get journeyCount => throw _privateConstructorUsedError;
  int get totalSessionTime => throw _privateConstructorUsedError;
  Map<String, dynamic> get preferences => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AdminUserEntity value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AdminUserEntity value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AdminUserEntity value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this AdminUserEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AdminUserEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdminUserEntityCopyWith<AdminUserEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminUserEntityCopyWith<$Res> {
  factory $AdminUserEntityCopyWith(
          AdminUserEntity value, $Res Function(AdminUserEntity) then) =
      _$AdminUserEntityCopyWithImpl<$Res, AdminUserEntity>;
  @useResult
  $Res call(
      {String id,
      String email,
      String name,
      String role,
      bool isActive,
      bool isEmailVerified,
      String? profileImageUrl,
      DateTime createdAt,
      DateTime? updatedAt,
      DateTime? lastLoginAt,
      int loginCount,
      int journeyCount,
      int totalSessionTime,
      Map<String, dynamic> preferences,
      List<String> tags,
      String? notes});
}

/// @nodoc
class _$AdminUserEntityCopyWithImpl<$Res, $Val extends AdminUserEntity>
    implements $AdminUserEntityCopyWith<$Res> {
  _$AdminUserEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminUserEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = null,
    Object? role = null,
    Object? isActive = null,
    Object? isEmailVerified = null,
    Object? profileImageUrl = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? lastLoginAt = freezed,
    Object? loginCount = null,
    Object? journeyCount = null,
    Object? totalSessionTime = null,
    Object? preferences = null,
    Object? tags = null,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loginCount: null == loginCount
          ? _value.loginCount
          : loginCount // ignore: cast_nullable_to_non_nullable
              as int,
      journeyCount: null == journeyCount
          ? _value.journeyCount
          : journeyCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalSessionTime: null == totalSessionTime
          ? _value.totalSessionTime
          : totalSessionTime // ignore: cast_nullable_to_non_nullable
              as int,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdminUserEntityImplCopyWith<$Res>
    implements $AdminUserEntityCopyWith<$Res> {
  factory _$$AdminUserEntityImplCopyWith(_$AdminUserEntityImpl value,
          $Res Function(_$AdminUserEntityImpl) then) =
      __$$AdminUserEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String name,
      String role,
      bool isActive,
      bool isEmailVerified,
      String? profileImageUrl,
      DateTime createdAt,
      DateTime? updatedAt,
      DateTime? lastLoginAt,
      int loginCount,
      int journeyCount,
      int totalSessionTime,
      Map<String, dynamic> preferences,
      List<String> tags,
      String? notes});
}

/// @nodoc
class __$$AdminUserEntityImplCopyWithImpl<$Res>
    extends _$AdminUserEntityCopyWithImpl<$Res, _$AdminUserEntityImpl>
    implements _$$AdminUserEntityImplCopyWith<$Res> {
  __$$AdminUserEntityImplCopyWithImpl(
      _$AdminUserEntityImpl _value, $Res Function(_$AdminUserEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminUserEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = null,
    Object? role = null,
    Object? isActive = null,
    Object? isEmailVerified = null,
    Object? profileImageUrl = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? lastLoginAt = freezed,
    Object? loginCount = null,
    Object? journeyCount = null,
    Object? totalSessionTime = null,
    Object? preferences = null,
    Object? tags = null,
    Object? notes = freezed,
  }) {
    return _then(_$AdminUserEntityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loginCount: null == loginCount
          ? _value.loginCount
          : loginCount // ignore: cast_nullable_to_non_nullable
              as int,
      journeyCount: null == journeyCount
          ? _value.journeyCount
          : journeyCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalSessionTime: null == totalSessionTime
          ? _value.totalSessionTime
          : totalSessionTime // ignore: cast_nullable_to_non_nullable
              as int,
      preferences: null == preferences
          ? _value._preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdminUserEntityImpl implements _AdminUserEntity {
  const _$AdminUserEntityImpl(
      {required this.id,
      required this.email,
      required this.name,
      this.role = 'user',
      this.isActive = true,
      this.isEmailVerified = false,
      this.profileImageUrl,
      required this.createdAt,
      this.updatedAt,
      this.lastLoginAt,
      this.loginCount = 0,
      this.journeyCount = 0,
      this.totalSessionTime = 0,
      final Map<String, dynamic> preferences = const {},
      final List<String> tags = const [],
      this.notes})
      : _preferences = preferences,
        _tags = tags;

  factory _$AdminUserEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdminUserEntityImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String name;
  @override
  @JsonKey()
  final String role;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  final String? profileImageUrl;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? lastLoginAt;
  @override
  @JsonKey()
  final int loginCount;
  @override
  @JsonKey()
  final int journeyCount;
  @override
  @JsonKey()
  final int totalSessionTime;
  final Map<String, dynamic> _preferences;
  @override
  @JsonKey()
  Map<String, dynamic> get preferences {
    if (_preferences is EqualUnmodifiableMapView) return _preferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_preferences);
  }

  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final String? notes;

  @override
  String toString() {
    return 'AdminUserEntity(id: $id, email: $email, name: $name, role: $role, isActive: $isActive, isEmailVerified: $isEmailVerified, profileImageUrl: $profileImageUrl, createdAt: $createdAt, updatedAt: $updatedAt, lastLoginAt: $lastLoginAt, loginCount: $loginCount, journeyCount: $journeyCount, totalSessionTime: $totalSessionTime, preferences: $preferences, tags: $tags, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdminUserEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.loginCount, loginCount) ||
                other.loginCount == loginCount) &&
            (identical(other.journeyCount, journeyCount) ||
                other.journeyCount == journeyCount) &&
            (identical(other.totalSessionTime, totalSessionTime) ||
                other.totalSessionTime == totalSessionTime) &&
            const DeepCollectionEquality()
                .equals(other._preferences, _preferences) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      email,
      name,
      role,
      isActive,
      isEmailVerified,
      profileImageUrl,
      createdAt,
      updatedAt,
      lastLoginAt,
      loginCount,
      journeyCount,
      totalSessionTime,
      const DeepCollectionEquality().hash(_preferences),
      const DeepCollectionEquality().hash(_tags),
      notes);

  /// Create a copy of AdminUserEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdminUserEntityImplCopyWith<_$AdminUserEntityImpl> get copyWith =>
      __$$AdminUserEntityImplCopyWithImpl<_$AdminUserEntityImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AdminUserEntity value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AdminUserEntity value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AdminUserEntity value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$AdminUserEntityImplToJson(
      this,
    );
  }
}

abstract class _AdminUserEntity implements AdminUserEntity {
  const factory _AdminUserEntity(
      {required final String id,
      required final String email,
      required final String name,
      final String role,
      final bool isActive,
      final bool isEmailVerified,
      final String? profileImageUrl,
      required final DateTime createdAt,
      final DateTime? updatedAt,
      final DateTime? lastLoginAt,
      final int loginCount,
      final int journeyCount,
      final int totalSessionTime,
      final Map<String, dynamic> preferences,
      final List<String> tags,
      final String? notes}) = _$AdminUserEntityImpl;

  factory _AdminUserEntity.fromJson(Map<String, dynamic> json) =
      _$AdminUserEntityImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String get name;
  @override
  String get role;
  @override
  bool get isActive;
  @override
  bool get isEmailVerified;
  @override
  String? get profileImageUrl;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get lastLoginAt;
  @override
  int get loginCount;
  @override
  int get journeyCount;
  @override
  int get totalSessionTime;
  @override
  Map<String, dynamic> get preferences;
  @override
  List<String> get tags;
  @override
  String? get notes;

  /// Create a copy of AdminUserEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdminUserEntityImplCopyWith<_$AdminUserEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
