// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_analytics_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserAnalyticsEntityImpl _$$UserAnalyticsEntityImplFromJson(
        Map<String, dynamic> json) =>
    _$UserAnalyticsEntityImpl(
      totalUsers: (json['totalUsers'] as num?)?.toInt() ?? 0,
      activeUsers: (json['activeUsers'] as num?)?.toInt() ?? 0,
      newUsersThisMonth: (json['newUsersThisMonth'] as num?)?.toInt() ?? 0,
      newUsersLastMonth: (json['newUsersLastMonth'] as num?)?.toInt() ?? 0,
      averageSessionDuration:
          (json['averageSessionDuration'] as num?)?.toDouble() ?? 0.0,
      totalSessions: (json['totalSessions'] as num?)?.toInt() ?? 0,
      totalJourneysCompleted:
          (json['totalJourneysCompleted'] as num?)?.toInt() ?? 0,
      averageJourneysPerUser:
          (json['averageJourneysPerUser'] as num?)?.toDouble() ?? 0.0,
      topJourneys: (json['topJourneys'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      userGrowthData: (json['userGrowthData'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      engagementMetrics:
          (json['engagementMetrics'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toInt()),
              ) ??
              const {},
      retentionRates: (json['retentionRates'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toDouble()),
          ) ??
          const {},
      deviceBreakdown: (json['deviceBreakdown'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      geographicData: (json['geographicData'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$$UserAnalyticsEntityImplToJson(
        _$UserAnalyticsEntityImpl instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'newUsersThisMonth': instance.newUsersThisMonth,
      'newUsersLastMonth': instance.newUsersLastMonth,
      'averageSessionDuration': instance.averageSessionDuration,
      'totalSessions': instance.totalSessions,
      'totalJourneysCompleted': instance.totalJourneysCompleted,
      'averageJourneysPerUser': instance.averageJourneysPerUser,
      'topJourneys': instance.topJourneys,
      'userGrowthData': instance.userGrowthData,
      'engagementMetrics': instance.engagementMetrics,
      'retentionRates': instance.retentionRates,
      'deviceBreakdown': instance.deviceBreakdown,
      'geographicData': instance.geographicData,
      'generatedAt': instance.generatedAt.toIso8601String(),
    };
