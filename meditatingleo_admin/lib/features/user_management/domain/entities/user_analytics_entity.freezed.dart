// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_analytics_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserAnalyticsEntity _$UserAnalyticsEntityFromJson(Map<String, dynamic> json) {
  return _UserAnalyticsEntity.fromJson(json);
}

/// @nodoc
mixin _$UserAnalyticsEntity {
  int get totalUsers => throw _privateConstructorUsedError;
  int get activeUsers => throw _privateConstructorUsedError;
  int get newUsersThisMonth => throw _privateConstructorUsedError;
  int get newUsersLastMonth => throw _privateConstructorUsedError;
  double get averageSessionDuration => throw _privateConstructorUsedError;
  int get totalSessions => throw _privateConstructorUsedError;
  int get totalJourneysCompleted => throw _privateConstructorUsedError;
  double get averageJourneysPerUser => throw _privateConstructorUsedError;
  List<String> get topJourneys => throw _privateConstructorUsedError;
  List<int> get userGrowthData => throw _privateConstructorUsedError;
  Map<String, int> get engagementMetrics => throw _privateConstructorUsedError;
  Map<String, double> get retentionRates => throw _privateConstructorUsedError;
  Map<String, int> get deviceBreakdown => throw _privateConstructorUsedError;
  Map<String, int> get geographicData => throw _privateConstructorUsedError;
  DateTime get generatedAt => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserAnalyticsEntity value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserAnalyticsEntity value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserAnalyticsEntity value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this UserAnalyticsEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserAnalyticsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserAnalyticsEntityCopyWith<UserAnalyticsEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserAnalyticsEntityCopyWith<$Res> {
  factory $UserAnalyticsEntityCopyWith(
          UserAnalyticsEntity value, $Res Function(UserAnalyticsEntity) then) =
      _$UserAnalyticsEntityCopyWithImpl<$Res, UserAnalyticsEntity>;
  @useResult
  $Res call(
      {int totalUsers,
      int activeUsers,
      int newUsersThisMonth,
      int newUsersLastMonth,
      double averageSessionDuration,
      int totalSessions,
      int totalJourneysCompleted,
      double averageJourneysPerUser,
      List<String> topJourneys,
      List<int> userGrowthData,
      Map<String, int> engagementMetrics,
      Map<String, double> retentionRates,
      Map<String, int> deviceBreakdown,
      Map<String, int> geographicData,
      DateTime generatedAt});
}

/// @nodoc
class _$UserAnalyticsEntityCopyWithImpl<$Res, $Val extends UserAnalyticsEntity>
    implements $UserAnalyticsEntityCopyWith<$Res> {
  _$UserAnalyticsEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserAnalyticsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalUsers = null,
    Object? activeUsers = null,
    Object? newUsersThisMonth = null,
    Object? newUsersLastMonth = null,
    Object? averageSessionDuration = null,
    Object? totalSessions = null,
    Object? totalJourneysCompleted = null,
    Object? averageJourneysPerUser = null,
    Object? topJourneys = null,
    Object? userGrowthData = null,
    Object? engagementMetrics = null,
    Object? retentionRates = null,
    Object? deviceBreakdown = null,
    Object? geographicData = null,
    Object? generatedAt = null,
  }) {
    return _then(_value.copyWith(
      totalUsers: null == totalUsers
          ? _value.totalUsers
          : totalUsers // ignore: cast_nullable_to_non_nullable
              as int,
      activeUsers: null == activeUsers
          ? _value.activeUsers
          : activeUsers // ignore: cast_nullable_to_non_nullable
              as int,
      newUsersThisMonth: null == newUsersThisMonth
          ? _value.newUsersThisMonth
          : newUsersThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      newUsersLastMonth: null == newUsersLastMonth
          ? _value.newUsersLastMonth
          : newUsersLastMonth // ignore: cast_nullable_to_non_nullable
              as int,
      averageSessionDuration: null == averageSessionDuration
          ? _value.averageSessionDuration
          : averageSessionDuration // ignore: cast_nullable_to_non_nullable
              as double,
      totalSessions: null == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int,
      totalJourneysCompleted: null == totalJourneysCompleted
          ? _value.totalJourneysCompleted
          : totalJourneysCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      averageJourneysPerUser: null == averageJourneysPerUser
          ? _value.averageJourneysPerUser
          : averageJourneysPerUser // ignore: cast_nullable_to_non_nullable
              as double,
      topJourneys: null == topJourneys
          ? _value.topJourneys
          : topJourneys // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userGrowthData: null == userGrowthData
          ? _value.userGrowthData
          : userGrowthData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      engagementMetrics: null == engagementMetrics
          ? _value.engagementMetrics
          : engagementMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      retentionRates: null == retentionRates
          ? _value.retentionRates
          : retentionRates // ignore: cast_nullable_to_non_nullable
              as Map<String, double>,
      deviceBreakdown: null == deviceBreakdown
          ? _value.deviceBreakdown
          : deviceBreakdown // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      geographicData: null == geographicData
          ? _value.geographicData
          : geographicData // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      generatedAt: null == generatedAt
          ? _value.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserAnalyticsEntityImplCopyWith<$Res>
    implements $UserAnalyticsEntityCopyWith<$Res> {
  factory _$$UserAnalyticsEntityImplCopyWith(_$UserAnalyticsEntityImpl value,
          $Res Function(_$UserAnalyticsEntityImpl) then) =
      __$$UserAnalyticsEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalUsers,
      int activeUsers,
      int newUsersThisMonth,
      int newUsersLastMonth,
      double averageSessionDuration,
      int totalSessions,
      int totalJourneysCompleted,
      double averageJourneysPerUser,
      List<String> topJourneys,
      List<int> userGrowthData,
      Map<String, int> engagementMetrics,
      Map<String, double> retentionRates,
      Map<String, int> deviceBreakdown,
      Map<String, int> geographicData,
      DateTime generatedAt});
}

/// @nodoc
class __$$UserAnalyticsEntityImplCopyWithImpl<$Res>
    extends _$UserAnalyticsEntityCopyWithImpl<$Res, _$UserAnalyticsEntityImpl>
    implements _$$UserAnalyticsEntityImplCopyWith<$Res> {
  __$$UserAnalyticsEntityImplCopyWithImpl(_$UserAnalyticsEntityImpl _value,
      $Res Function(_$UserAnalyticsEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserAnalyticsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalUsers = null,
    Object? activeUsers = null,
    Object? newUsersThisMonth = null,
    Object? newUsersLastMonth = null,
    Object? averageSessionDuration = null,
    Object? totalSessions = null,
    Object? totalJourneysCompleted = null,
    Object? averageJourneysPerUser = null,
    Object? topJourneys = null,
    Object? userGrowthData = null,
    Object? engagementMetrics = null,
    Object? retentionRates = null,
    Object? deviceBreakdown = null,
    Object? geographicData = null,
    Object? generatedAt = null,
  }) {
    return _then(_$UserAnalyticsEntityImpl(
      totalUsers: null == totalUsers
          ? _value.totalUsers
          : totalUsers // ignore: cast_nullable_to_non_nullable
              as int,
      activeUsers: null == activeUsers
          ? _value.activeUsers
          : activeUsers // ignore: cast_nullable_to_non_nullable
              as int,
      newUsersThisMonth: null == newUsersThisMonth
          ? _value.newUsersThisMonth
          : newUsersThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      newUsersLastMonth: null == newUsersLastMonth
          ? _value.newUsersLastMonth
          : newUsersLastMonth // ignore: cast_nullable_to_non_nullable
              as int,
      averageSessionDuration: null == averageSessionDuration
          ? _value.averageSessionDuration
          : averageSessionDuration // ignore: cast_nullable_to_non_nullable
              as double,
      totalSessions: null == totalSessions
          ? _value.totalSessions
          : totalSessions // ignore: cast_nullable_to_non_nullable
              as int,
      totalJourneysCompleted: null == totalJourneysCompleted
          ? _value.totalJourneysCompleted
          : totalJourneysCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      averageJourneysPerUser: null == averageJourneysPerUser
          ? _value.averageJourneysPerUser
          : averageJourneysPerUser // ignore: cast_nullable_to_non_nullable
              as double,
      topJourneys: null == topJourneys
          ? _value._topJourneys
          : topJourneys // ignore: cast_nullable_to_non_nullable
              as List<String>,
      userGrowthData: null == userGrowthData
          ? _value._userGrowthData
          : userGrowthData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      engagementMetrics: null == engagementMetrics
          ? _value._engagementMetrics
          : engagementMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      retentionRates: null == retentionRates
          ? _value._retentionRates
          : retentionRates // ignore: cast_nullable_to_non_nullable
              as Map<String, double>,
      deviceBreakdown: null == deviceBreakdown
          ? _value._deviceBreakdown
          : deviceBreakdown // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      geographicData: null == geographicData
          ? _value._geographicData
          : geographicData // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      generatedAt: null == generatedAt
          ? _value.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserAnalyticsEntityImpl implements _UserAnalyticsEntity {
  const _$UserAnalyticsEntityImpl(
      {this.totalUsers = 0,
      this.activeUsers = 0,
      this.newUsersThisMonth = 0,
      this.newUsersLastMonth = 0,
      this.averageSessionDuration = 0.0,
      this.totalSessions = 0,
      this.totalJourneysCompleted = 0,
      this.averageJourneysPerUser = 0.0,
      final List<String> topJourneys = const [],
      final List<int> userGrowthData = const [],
      final Map<String, int> engagementMetrics = const {},
      final Map<String, double> retentionRates = const {},
      final Map<String, int> deviceBreakdown = const {},
      final Map<String, int> geographicData = const {},
      required this.generatedAt})
      : _topJourneys = topJourneys,
        _userGrowthData = userGrowthData,
        _engagementMetrics = engagementMetrics,
        _retentionRates = retentionRates,
        _deviceBreakdown = deviceBreakdown,
        _geographicData = geographicData;

  factory _$UserAnalyticsEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserAnalyticsEntityImplFromJson(json);

  @override
  @JsonKey()
  final int totalUsers;
  @override
  @JsonKey()
  final int activeUsers;
  @override
  @JsonKey()
  final int newUsersThisMonth;
  @override
  @JsonKey()
  final int newUsersLastMonth;
  @override
  @JsonKey()
  final double averageSessionDuration;
  @override
  @JsonKey()
  final int totalSessions;
  @override
  @JsonKey()
  final int totalJourneysCompleted;
  @override
  @JsonKey()
  final double averageJourneysPerUser;
  final List<String> _topJourneys;
  @override
  @JsonKey()
  List<String> get topJourneys {
    if (_topJourneys is EqualUnmodifiableListView) return _topJourneys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topJourneys);
  }

  final List<int> _userGrowthData;
  @override
  @JsonKey()
  List<int> get userGrowthData {
    if (_userGrowthData is EqualUnmodifiableListView) return _userGrowthData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userGrowthData);
  }

  final Map<String, int> _engagementMetrics;
  @override
  @JsonKey()
  Map<String, int> get engagementMetrics {
    if (_engagementMetrics is EqualUnmodifiableMapView)
      return _engagementMetrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_engagementMetrics);
  }

  final Map<String, double> _retentionRates;
  @override
  @JsonKey()
  Map<String, double> get retentionRates {
    if (_retentionRates is EqualUnmodifiableMapView) return _retentionRates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_retentionRates);
  }

  final Map<String, int> _deviceBreakdown;
  @override
  @JsonKey()
  Map<String, int> get deviceBreakdown {
    if (_deviceBreakdown is EqualUnmodifiableMapView) return _deviceBreakdown;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_deviceBreakdown);
  }

  final Map<String, int> _geographicData;
  @override
  @JsonKey()
  Map<String, int> get geographicData {
    if (_geographicData is EqualUnmodifiableMapView) return _geographicData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_geographicData);
  }

  @override
  final DateTime generatedAt;

  @override
  String toString() {
    return 'UserAnalyticsEntity(totalUsers: $totalUsers, activeUsers: $activeUsers, newUsersThisMonth: $newUsersThisMonth, newUsersLastMonth: $newUsersLastMonth, averageSessionDuration: $averageSessionDuration, totalSessions: $totalSessions, totalJourneysCompleted: $totalJourneysCompleted, averageJourneysPerUser: $averageJourneysPerUser, topJourneys: $topJourneys, userGrowthData: $userGrowthData, engagementMetrics: $engagementMetrics, retentionRates: $retentionRates, deviceBreakdown: $deviceBreakdown, geographicData: $geographicData, generatedAt: $generatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserAnalyticsEntityImpl &&
            (identical(other.totalUsers, totalUsers) ||
                other.totalUsers == totalUsers) &&
            (identical(other.activeUsers, activeUsers) ||
                other.activeUsers == activeUsers) &&
            (identical(other.newUsersThisMonth, newUsersThisMonth) ||
                other.newUsersThisMonth == newUsersThisMonth) &&
            (identical(other.newUsersLastMonth, newUsersLastMonth) ||
                other.newUsersLastMonth == newUsersLastMonth) &&
            (identical(other.averageSessionDuration, averageSessionDuration) ||
                other.averageSessionDuration == averageSessionDuration) &&
            (identical(other.totalSessions, totalSessions) ||
                other.totalSessions == totalSessions) &&
            (identical(other.totalJourneysCompleted, totalJourneysCompleted) ||
                other.totalJourneysCompleted == totalJourneysCompleted) &&
            (identical(other.averageJourneysPerUser, averageJourneysPerUser) ||
                other.averageJourneysPerUser == averageJourneysPerUser) &&
            const DeepCollectionEquality()
                .equals(other._topJourneys, _topJourneys) &&
            const DeepCollectionEquality()
                .equals(other._userGrowthData, _userGrowthData) &&
            const DeepCollectionEquality()
                .equals(other._engagementMetrics, _engagementMetrics) &&
            const DeepCollectionEquality()
                .equals(other._retentionRates, _retentionRates) &&
            const DeepCollectionEquality()
                .equals(other._deviceBreakdown, _deviceBreakdown) &&
            const DeepCollectionEquality()
                .equals(other._geographicData, _geographicData) &&
            (identical(other.generatedAt, generatedAt) ||
                other.generatedAt == generatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalUsers,
      activeUsers,
      newUsersThisMonth,
      newUsersLastMonth,
      averageSessionDuration,
      totalSessions,
      totalJourneysCompleted,
      averageJourneysPerUser,
      const DeepCollectionEquality().hash(_topJourneys),
      const DeepCollectionEquality().hash(_userGrowthData),
      const DeepCollectionEquality().hash(_engagementMetrics),
      const DeepCollectionEquality().hash(_retentionRates),
      const DeepCollectionEquality().hash(_deviceBreakdown),
      const DeepCollectionEquality().hash(_geographicData),
      generatedAt);

  /// Create a copy of UserAnalyticsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserAnalyticsEntityImplCopyWith<_$UserAnalyticsEntityImpl> get copyWith =>
      __$$UserAnalyticsEntityImplCopyWithImpl<_$UserAnalyticsEntityImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserAnalyticsEntity value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserAnalyticsEntity value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserAnalyticsEntity value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$UserAnalyticsEntityImplToJson(
      this,
    );
  }
}

abstract class _UserAnalyticsEntity implements UserAnalyticsEntity {
  const factory _UserAnalyticsEntity(
      {final int totalUsers,
      final int activeUsers,
      final int newUsersThisMonth,
      final int newUsersLastMonth,
      final double averageSessionDuration,
      final int totalSessions,
      final int totalJourneysCompleted,
      final double averageJourneysPerUser,
      final List<String> topJourneys,
      final List<int> userGrowthData,
      final Map<String, int> engagementMetrics,
      final Map<String, double> retentionRates,
      final Map<String, int> deviceBreakdown,
      final Map<String, int> geographicData,
      required final DateTime generatedAt}) = _$UserAnalyticsEntityImpl;

  factory _UserAnalyticsEntity.fromJson(Map<String, dynamic> json) =
      _$UserAnalyticsEntityImpl.fromJson;

  @override
  int get totalUsers;
  @override
  int get activeUsers;
  @override
  int get newUsersThisMonth;
  @override
  int get newUsersLastMonth;
  @override
  double get averageSessionDuration;
  @override
  int get totalSessions;
  @override
  int get totalJourneysCompleted;
  @override
  double get averageJourneysPerUser;
  @override
  List<String> get topJourneys;
  @override
  List<int> get userGrowthData;
  @override
  Map<String, int> get engagementMetrics;
  @override
  Map<String, double> get retentionRates;
  @override
  Map<String, int> get deviceBreakdown;
  @override
  Map<String, int> get geographicData;
  @override
  DateTime get generatedAt;

  /// Create a copy of UserAnalyticsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserAnalyticsEntityImplCopyWith<_$UserAnalyticsEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
