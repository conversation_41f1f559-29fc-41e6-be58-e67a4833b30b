import 'package:freezed_annotation/freezed_annotation.dart';

part 'admin_user_entity.freezed.dart';
part 'admin_user_entity.g.dart';

/// [AdminUserEntity] represents a user in the admin management system.
///
/// This entity provides a comprehensive view of user data for administrative
/// purposes, including account status, activity metrics, and management metadata.
///
/// Features:
/// - Immutable data structure using Freezed
/// - JSON serialization support
/// - Role-based access information
/// - Activity tracking
/// - Account status management
@freezed
class AdminUserEntity with _$AdminUserEntity {
  /// Creates an [AdminUserEntity] instance.
  ///
  /// Parameters:
  /// - [id]: Unique identifier for the user
  /// - [email]: User's email address (primary identifier)
  /// - [name]: User's display name
  /// - [role]: User's role (user, admin, super_admin)
  /// - [isActive]: Whether the user account is active
  /// - [isEmailVerified]: Whether the user's email is verified
  /// - [profileImageUrl]: Optional URL to user's profile image
  /// - [createdAt]: When the user account was created
  /// - [updatedAt]: When the user account was last updated
  /// - [lastLoginAt]: When the user last logged in
  /// - [loginCount]: Total number of logins
  /// - [journeyCount]: Number of journeys completed
  /// - [totalSessionTime]: Total time spent in the app (minutes)
  /// - [preferences]: User preferences as JSON
  /// - [tags]: Administrative tags for user categorization
  /// - [notes]: Administrative notes about the user
  const factory AdminUserEntity({
    required String id,
    required String email,
    required String name,
    @Default('user') String role,
    @Default(true) bool isActive,
    @Default(false) bool isEmailVerified,
    String? profileImageUrl,
    required DateTime createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    @Default(0) int loginCount,
    @Default(0) int journeyCount,
    @Default(0) int totalSessionTime,
    @Default({}) Map<String, dynamic> preferences,
    @Default([]) List<String> tags,
    String? notes,
  }) = _AdminUserEntity;

  /// Creates an [AdminUserEntity] from JSON.
  factory AdminUserEntity.fromJson(Map<String, dynamic> json) =>
      _$AdminUserEntityFromJson(json);
}

/// Extension methods for [AdminUserEntity] to provide additional functionality
extension AdminUserEntityExtensions on AdminUserEntity {
  /// Returns true if the user is an admin (admin or super_admin role)
  bool get isAdmin => role == 'admin' || role == 'super_admin';

  /// Returns true if the user is a super admin
  bool get isSuperAdmin => role == 'super_admin';

  /// Returns true if the user is a regular user
  bool get isRegularUser => role == 'user';

  /// Returns true if the user has logged in recently (within 30 days)
  bool get isRecentlyActive {
    if (lastLoginAt == null) return false;
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return lastLoginAt!.isAfter(thirtyDaysAgo);
  }

  /// Returns true if the user is a new user (created within 7 days)
  bool get isNewUser {
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    return createdAt.isAfter(sevenDaysAgo);
  }

  /// Returns the user's engagement level based on activity
  String get engagementLevel {
    if (journeyCount >= 10 && totalSessionTime >= 300) return 'High';
    if (journeyCount >= 3 && totalSessionTime >= 60) return 'Medium';
    if (journeyCount >= 1 || totalSessionTime >= 15) return 'Low';
    return 'None';
  }

  /// Returns the average session time in minutes
  double get averageSessionTime {
    if (loginCount == 0) return 0.0;
    return totalSessionTime / loginCount;
  }

  /// Returns the number of days since the user was created
  int get daysSinceCreated {
    return DateTime.now().difference(createdAt).inDays;
  }

  /// Returns the number of days since the user last logged in
  int? get daysSinceLastLogin {
    if (lastLoginAt == null) return null;
    return DateTime.now().difference(lastLoginAt!).inDays;
  }

  /// Returns a copy of the user with updated activity
  AdminUserEntity updateActivity({
    DateTime? lastLogin,
    int? additionalSessionTime,
  }) {
    return copyWith(
      lastLoginAt: lastLogin ?? lastLoginAt,
      loginCount: lastLogin != null ? loginCount + 1 : loginCount,
      totalSessionTime: additionalSessionTime != null
          ? totalSessionTime + additionalSessionTime
          : totalSessionTime,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with updated status
  AdminUserEntity updateStatus(bool active) {
    return copyWith(
      isActive: active,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with updated role
  AdminUserEntity updateRole(String newRole) {
    return copyWith(
      role: newRole,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with updated email verification
  AdminUserEntity verifyEmail() {
    return copyWith(
      isEmailVerified: true,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with added tag
  AdminUserEntity addTag(String tag) {
    if (tags.contains(tag)) return this;
    return copyWith(
      tags: [...tags, tag],
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with removed tag
  AdminUserEntity removeTag(String tag) {
    return copyWith(
      tags: tags.where((t) => t != tag).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the user with updated notes
  AdminUserEntity updateNotes(String? newNotes) {
    return copyWith(
      notes: newNotes,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a summary string for display purposes
  String get summary =>
      '$name ($email) - $role - ${isActive ? 'Active' : 'Inactive'}';

  /// Returns the user's status as a display string
  String get statusDisplay {
    if (!isActive) return 'Inactive';
    if (!isEmailVerified) return 'Unverified';
    if (isRecentlyActive) return 'Active';
    return 'Dormant';
  }

  /// Returns a color code for the user's status
  String get statusColor {
    if (!isActive) return 'red';
    if (!isEmailVerified) return 'orange';
    if (isRecentlyActive) return 'green';
    return 'gray';
  }
}
