// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_user_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdminUserEntityImpl _$$AdminUserEntityImplFromJson(
        Map<String, dynamic> json) =>
    _$AdminUserEntityImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      role: json['role'] as String? ?? 'user',
      isActive: json['isActive'] as bool? ?? true,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      profileImageUrl: json['profileImageUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      loginCount: (json['loginCount'] as num?)?.toInt() ?? 0,
      journeyCount: (json['journeyCount'] as num?)?.toInt() ?? 0,
      totalSessionTime: (json['totalSessionTime'] as num?)?.toInt() ?? 0,
      preferences: json['preferences'] as Map<String, dynamic>? ?? const {},
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$AdminUserEntityImplToJson(
        _$AdminUserEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'role': instance.role,
      'isActive': instance.isActive,
      'isEmailVerified': instance.isEmailVerified,
      'profileImageUrl': instance.profileImageUrl,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'loginCount': instance.loginCount,
      'journeyCount': instance.journeyCount,
      'totalSessionTime': instance.totalSessionTime,
      'preferences': instance.preferences,
      'tags': instance.tags,
      'notes': instance.notes,
    };
