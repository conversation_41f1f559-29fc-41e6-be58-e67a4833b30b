import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_analytics_entity.freezed.dart';
part 'user_analytics_entity.g.dart';

/// [UserAnalyticsEntity] represents comprehensive user analytics data for the admin panel.
///
/// This entity provides aggregated metrics and insights about user behavior,
/// engagement, and system usage for administrative decision-making.
///
/// Features:
/// - Immutable data structure using Freezed
/// - JSON serialization support
/// - Comprehensive user metrics
/// - Growth and engagement tracking
/// - Time-series data support
@freezed
class UserAnalyticsEntity with _$UserAnalyticsEntity {
  /// Creates a [UserAnalyticsEntity] instance.
  ///
  /// Parameters:
  /// - [totalUsers]: Total number of registered users
  /// - [activeUsers]: Number of users active in the last 30 days
  /// - [newUsersThisMonth]: Number of users registered this month
  /// - [newUsersLastMonth]: Number of users registered last month
  /// - [averageSessionDuration]: Average session duration in minutes
  /// - [totalSessions]: Total number of user sessions
  /// - [totalJourneysCompleted]: Total number of journeys completed by all users
  /// - [averageJourneysPerUser]: Average number of journeys per user
  /// - [topJourneys]: List of most popular journey titles
  /// - [userGrowthData]: Monthly user growth data (last 12 months)
  /// - [engagementMetrics]: Various engagement metrics
  /// - [retentionRates]: User retention rates by time period
  /// - [deviceBreakdown]: Breakdown of users by device type
  /// - [geographicData]: Geographic distribution of users
  /// - [generatedAt]: When this analytics data was generated
  const factory UserAnalyticsEntity({
    @Default(0) int totalUsers,
    @Default(0) int activeUsers,
    @Default(0) int newUsersThisMonth,
    @Default(0) int newUsersLastMonth,
    @Default(0.0) double averageSessionDuration,
    @Default(0) int totalSessions,
    @Default(0) int totalJourneysCompleted,
    @Default(0.0) double averageJourneysPerUser,
    @Default([]) List<String> topJourneys,
    @Default([]) List<int> userGrowthData,
    @Default({}) Map<String, int> engagementMetrics,
    @Default({}) Map<String, double> retentionRates,
    @Default({}) Map<String, int> deviceBreakdown,
    @Default({}) Map<String, int> geographicData,
    required DateTime generatedAt,
  }) = _UserAnalyticsEntity;

  /// Creates a [UserAnalyticsEntity] from JSON.
  factory UserAnalyticsEntity.fromJson(Map<String, dynamic> json) =>
      _$UserAnalyticsEntityFromJson(json);
}

/// Extension methods for [UserAnalyticsEntity] to provide additional functionality
extension UserAnalyticsEntityExtensions on UserAnalyticsEntity {
  /// Returns the user growth rate compared to last month
  double get userGrowthRate {
    if (newUsersLastMonth == 0) return 0.0;
    return ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100;
  }

  /// Returns the user activity rate (active users / total users)
  double get userActivityRate {
    if (totalUsers == 0) return 0.0;
    return (activeUsers / totalUsers) * 100;
  }

  /// Returns the average sessions per user
  double get averageSessionsPerUser {
    if (totalUsers == 0) return 0.0;
    return totalSessions / totalUsers;
  }

  /// Returns the journey completion rate
  double get journeyCompletionRate {
    if (totalSessions == 0) return 0.0;
    return (totalJourneysCompleted / totalSessions) * 100;
  }

  /// Returns true if user growth is positive
  bool get hasPositiveGrowth => userGrowthRate > 0;

  /// Returns true if user activity is high (>70%)
  bool get hasHighActivity => userActivityRate > 70;

  /// Returns the most popular journey
  String? get mostPopularJourney =>
      topJourneys.isNotEmpty ? topJourneys.first : null;

  /// Returns the total growth over the tracked period
  int get totalGrowthOverPeriod {
    if (userGrowthData.isEmpty) return 0;
    return userGrowthData.reduce((a, b) => a + b);
  }

  /// Returns the average monthly growth
  double get averageMonthlyGrowth {
    if (userGrowthData.isEmpty) return 0.0;
    return totalGrowthOverPeriod / userGrowthData.length;
  }

  /// Returns the peak growth month value
  int get peakGrowthMonth {
    if (userGrowthData.isEmpty) return 0;
    return userGrowthData.reduce((a, b) => a > b ? a : b);
  }

  /// Returns the lowest growth month value
  int get lowestGrowthMonth {
    if (userGrowthData.isEmpty) return 0;
    return userGrowthData.reduce((a, b) => a < b ? a : b);
  }

  /// Returns daily active users (if available in engagement metrics)
  int get dailyActiveUsers => engagementMetrics['daily_active'] ?? 0;

  /// Returns weekly active users (if available in engagement metrics)
  int get weeklyActiveUsers => engagementMetrics['weekly_active'] ?? 0;

  /// Returns monthly active users (if available in engagement metrics)
  int get monthlyActiveUsers =>
      engagementMetrics['monthly_active'] ?? activeUsers;

  /// Returns the 7-day retention rate
  double get retention7Day => retentionRates['7_day'] ?? 0.0;

  /// Returns the 30-day retention rate
  double get retention30Day => retentionRates['30_day'] ?? 0.0;

  /// Returns the 90-day retention rate
  double get retention90Day => retentionRates['90_day'] ?? 0.0;

  /// Returns the primary device type
  String get primaryDeviceType {
    if (deviceBreakdown.isEmpty) return 'Unknown';
    return deviceBreakdown.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Returns the primary geographic region
  String get primaryRegion {
    if (geographicData.isEmpty) return 'Unknown';
    return geographicData.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Returns a summary of key metrics
  Map<String, dynamic> get keyMetricsSummary => {
        'total_users': totalUsers,
        'active_users': activeUsers,
        'growth_rate': userGrowthRate.toStringAsFixed(1),
        'activity_rate': userActivityRate.toStringAsFixed(1),
        'avg_session_duration': averageSessionDuration.toStringAsFixed(1),
        'journey_completion_rate': journeyCompletionRate.toStringAsFixed(1),
        'retention_7_day': retention7Day.toStringAsFixed(1),
        'retention_30_day': retention30Day.toStringAsFixed(1),
      };

  /// Returns engagement level based on activity metrics
  String get engagementLevel {
    if (userActivityRate >= 80 && retention30Day >= 60) return 'Excellent';
    if (userActivityRate >= 60 && retention30Day >= 40) return 'Good';
    if (userActivityRate >= 40 && retention30Day >= 20) return 'Fair';
    return 'Poor';
  }

  /// Returns growth trend based on recent data
  String get growthTrend {
    if (userGrowthData.length < 3) return 'Insufficient Data';

    final recent = userGrowthData.length >= 3
        ? userGrowthData.sublist(userGrowthData.length - 3)
        : userGrowthData;
    if (recent[2] > recent[1] && recent[1] > recent[0]) return 'Accelerating';
    if (recent[2] > recent[0]) return 'Growing';
    if (recent[2] == recent[0]) return 'Stable';
    return 'Declining';
  }

  /// Returns a health score (0-100) based on key metrics
  int get healthScore {
    double score = 0;

    // Activity rate (30% weight)
    score += (userActivityRate / 100) * 30;

    // Growth rate (20% weight) - cap at 50% for scoring
    final cappedGrowthRate = userGrowthRate.clamp(-50, 50);
    score += ((cappedGrowthRate + 50) / 100) * 20;

    // 30-day retention (25% weight)
    score += (retention30Day / 100) * 25;

    // Journey completion rate (15% weight)
    score += (journeyCompletionRate / 100) * 15;

    // Session duration (10% weight) - assume 30 minutes is ideal
    final sessionScore = (averageSessionDuration / 30).clamp(0, 1);
    score += sessionScore * 10;

    return score.round().clamp(0, 100);
  }

  /// Returns the age of this analytics data in hours
  int get dataAgeInHours {
    return DateTime.now().difference(generatedAt).inHours;
  }

  /// Returns true if the analytics data is fresh (less than 24 hours old)
  bool get isFresh => dataAgeInHours < 24;

  /// Returns true if the analytics data is stale (more than 48 hours old)
  bool get isStale => dataAgeInHours > 48;
}
