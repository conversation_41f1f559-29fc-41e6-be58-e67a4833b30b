// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_management_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userRepositoryHash() => r'7b0a753937089323baaa595819fef5d1f485f5cd';

/// Provider for the user repository
///
/// Copied from [userRepository].
@ProviderFor(userRepository)
final userRepositoryProvider = AutoDisposeProvider<UserRepository>.internal(
  userRepository,
  name: r'userRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserRepositoryRef = AutoDisposeProviderRef<UserRepository>;
String _$userManagementNotifierHash() =>
    r'ffddc85df77aa867fd09477b7eac3c98c3e1bea1';

/// [UserManagementNotifier] manages the state for user administration operations.
///
/// Provides:
/// - User loading and management
/// - User search and filtering
/// - User status and role updates
/// - User deletion
///
/// Depends on:
/// - UserRepository for data operations
///
/// Copied from [UserManagementNotifier].
@ProviderFor(UserManagementNotifier)
final userManagementNotifierProvider = AutoDisposeNotifierProvider<
    UserManagementNotifier, AsyncValue<UserManagementState>>.internal(
  UserManagementNotifier.new,
  name: r'userManagementNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userManagementNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserManagementNotifier
    = AutoDisposeNotifier<AsyncValue<UserManagementState>>;
String _$userAnalyticsNotifierHash() =>
    r'd52c12d8642a3ee4a167648bcf8e172ed54787c9';

/// [UserAnalyticsNotifier] manages the state for user analytics and metrics.
///
/// Provides:
/// - User analytics loading
/// - Metrics calculation
/// - Analytics refresh
///
/// Depends on:
/// - UserRepository for analytics data
///
/// Copied from [UserAnalyticsNotifier].
@ProviderFor(UserAnalyticsNotifier)
final userAnalyticsNotifierProvider = AutoDisposeNotifierProvider<
    UserAnalyticsNotifier, AsyncValue<UserAnalyticsState>>.internal(
  UserAnalyticsNotifier.new,
  name: r'userAnalyticsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userAnalyticsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserAnalyticsNotifier
    = AutoDisposeNotifier<AsyncValue<UserAnalyticsState>>;
String _$bulkOperationsNotifierHash() =>
    r'f96eef6634a45ffade55495dadda0739bf2b8751';

/// [BulkOperationsNotifier] manages the state for bulk user operations.
///
/// Provides:
/// - User selection for bulk operations
/// - Bulk status updates
/// - Bulk role updates
/// - Bulk deletion
///
/// Depends on:
/// - UserRepository for bulk operations
///
/// Copied from [BulkOperationsNotifier].
@ProviderFor(BulkOperationsNotifier)
final bulkOperationsNotifierProvider = AutoDisposeNotifierProvider<
    BulkOperationsNotifier, AsyncValue<BulkOperationsState>>.internal(
  BulkOperationsNotifier.new,
  name: r'bulkOperationsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bulkOperationsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BulkOperationsNotifier
    = AutoDisposeNotifier<AsyncValue<BulkOperationsState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
