import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../domain/entities/admin_user_entity.dart';
import '../../domain/entities/user_analytics_entity.dart';
import '../../../../core/repositories/user_repository.dart';
import '../../../../shared/providers/database_providers.dart';

part 'user_management_providers.g.dart';

/// Provider for the user repository
@riverpod
UserRepository userRepository(UserRepositoryRef ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final database = ref.watch(adminDatabaseProvider);
  return UserRepository(supabaseService, database);
}

/// State class for user management
class UserManagementState {
  final List<AdminUserEntity> users;
  final AdminUserEntity? selectedUser;
  final bool isLoading;
  final String? errorMessage;
  final String searchQuery;
  final String? roleFilter;
  final bool? statusFilter;

  const UserManagementState({
    this.users = const [],
    this.selectedUser,
    this.isLoading = false,
    this.errorMessage,
    this.searchQuery = '',
    this.roleFilter,
    this.statusFilter,
  });

  UserManagementState copyWith({
    List<AdminUserEntity>? users,
    AdminUserEntity? selectedUser,
    bool? isLoading,
    String? errorMessage,
    String? searchQuery,
    String? roleFilter,
    bool? statusFilter,
  }) {
    return UserManagementState(
      users: users ?? this.users,
      selectedUser: selectedUser ?? this.selectedUser,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      searchQuery: searchQuery ?? this.searchQuery,
      roleFilter: roleFilter ?? this.roleFilter,
      statusFilter: statusFilter ?? this.statusFilter,
    );
  }
}

/// State class for user analytics
class UserAnalyticsState {
  final int totalUsers;
  final int activeUsers;
  final int newUsersThisMonth;
  final double averageSessionDuration;
  final List<String> topJourneys;
  final List<int> userGrowthData;
  final Map<String, int> engagementMetrics;
  final bool isLoading;
  final String? errorMessage;
  final DateTime? lastUpdated;

  const UserAnalyticsState({
    this.totalUsers = 0,
    this.activeUsers = 0,
    this.newUsersThisMonth = 0,
    this.averageSessionDuration = 0.0,
    this.topJourneys = const [],
    this.userGrowthData = const [],
    this.engagementMetrics = const {},
    this.isLoading = false,
    this.errorMessage,
    this.lastUpdated,
  });

  UserAnalyticsState copyWith({
    int? totalUsers,
    int? activeUsers,
    int? newUsersThisMonth,
    double? averageSessionDuration,
    List<String>? topJourneys,
    List<int>? userGrowthData,
    Map<String, int>? engagementMetrics,
    bool? isLoading,
    String? errorMessage,
    DateTime? lastUpdated,
  }) {
    return UserAnalyticsState(
      totalUsers: totalUsers ?? this.totalUsers,
      activeUsers: activeUsers ?? this.activeUsers,
      newUsersThisMonth: newUsersThisMonth ?? this.newUsersThisMonth,
      averageSessionDuration: averageSessionDuration ?? this.averageSessionDuration,
      topJourneys: topJourneys ?? this.topJourneys,
      userGrowthData: userGrowthData ?? this.userGrowthData,
      engagementMetrics: engagementMetrics ?? this.engagementMetrics,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// State class for bulk operations
class BulkOperationsState {
  final List<String> selectedUserIds;
  final bool isProcessing;
  final String? errorMessage;
  final String? successMessage;

  const BulkOperationsState({
    this.selectedUserIds = const [],
    this.isProcessing = false,
    this.errorMessage,
    this.successMessage,
  });

  BulkOperationsState copyWith({
    List<String>? selectedUserIds,
    bool? isProcessing,
    String? errorMessage,
    String? successMessage,
  }) {
    return BulkOperationsState(
      selectedUserIds: selectedUserIds ?? this.selectedUserIds,
      isProcessing: isProcessing ?? this.isProcessing,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
    );
  }
}

/// [UserManagementNotifier] manages the state for user administration operations.
///
/// Provides:
/// - User loading and management
/// - User search and filtering
/// - User status and role updates
/// - User deletion
///
/// Depends on:
/// - UserRepository for data operations
@riverpod
class UserManagementNotifier extends _$UserManagementNotifier {
  @override
  AsyncValue<UserManagementState> build() {
    return const AsyncValue.data(UserManagementState());
  }

  /// Loads all users from the repository
  Future<void> loadUsers() async {
    state = const AsyncValue.loading();
    
    try {
      final repository = ref.read(userRepositoryProvider);
      final users = await repository.getAllUsers();
      
      state = AsyncValue.data(
        const UserManagementState().copyWith(users: users),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Selects a user for detailed viewing/editing
  Future<void> selectUser(AdminUserEntity user) async {
    final currentState = state.value ?? const UserManagementState();
    state = AsyncValue.data(
      currentState.copyWith(selectedUser: user),
    );
  }

  /// Clears the selected user
  Future<void> clearSelection() async {
    final currentState = state.value ?? const UserManagementState();
    state = AsyncValue.data(
      currentState.copyWith(selectedUser: null),
    );
  }

  /// Updates a user's status
  Future<void> updateUserStatus(String userId, bool isActive) async {
    try {
      final repository = ref.read(userRepositoryProvider);
      await repository.updateUserStatus(userId, isActive);
      
      // Reload users to reflect the update
      await loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Updates a user's role
  Future<void> updateUserRole(String userId, String role) async {
    try {
      final repository = ref.read(userRepositoryProvider);
      await repository.updateUserRole(userId, role);
      
      // Reload users to reflect the update
      await loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Deletes a user
  Future<void> deleteUser(String userId) async {
    try {
      final repository = ref.read(userRepositoryProvider);
      await repository.deleteUser(userId);
      
      // Reload users to reflect the deletion
      await loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Searches users by query
  Future<void> searchUsers(String query) async {
    final currentState = state.value ?? const UserManagementState();
    
    try {
      final repository = ref.read(userRepositoryProvider);
      final users = query.isEmpty 
          ? await repository.getAllUsers()
          : await repository.searchUsers(query);
      
      state = AsyncValue.data(
        currentState.copyWith(
          users: users,
          searchQuery: query,
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Filters users by role
  Future<void> filterByRole(String? role) async {
    final currentState = state.value ?? const UserManagementState();
    
    try {
      final repository = ref.read(userRepositoryProvider);
      final users = role == null 
          ? await repository.getAllUsers()
          : await repository.getUsersByRole(role);
      
      state = AsyncValue.data(
        currentState.copyWith(
          users: users,
          roleFilter: role,
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Filters users by status
  Future<void> filterByStatus(bool? isActive) async {
    final currentState = state.value ?? const UserManagementState();
    
    try {
      final repository = ref.read(userRepositoryProvider);
      final users = isActive == null 
          ? await repository.getAllUsers()
          : await repository.getUsersByStatus(isActive);
      
      state = AsyncValue.data(
        currentState.copyWith(
          users: users,
          statusFilter: isActive,
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// [UserAnalyticsNotifier] manages the state for user analytics and metrics.
///
/// Provides:
/// - User analytics loading
/// - Metrics calculation
/// - Analytics refresh
///
/// Depends on:
/// - UserRepository for analytics data
@riverpod
class UserAnalyticsNotifier extends _$UserAnalyticsNotifier {
  @override
  AsyncValue<UserAnalyticsState> build() {
    return const AsyncValue.data(UserAnalyticsState());
  }

  /// Loads user analytics from the repository
  Future<void> loadAnalytics() async {
    state = const AsyncValue.loading();
    
    try {
      final repository = ref.read(userRepositoryProvider);
      final analytics = await repository.getUserAnalytics();
      
      state = AsyncValue.data(
        UserAnalyticsState(
          totalUsers: analytics.totalUsers,
          activeUsers: analytics.activeUsers,
          newUsersThisMonth: analytics.newUsersThisMonth,
          averageSessionDuration: analytics.averageSessionDuration,
          topJourneys: analytics.topJourneys,
          userGrowthData: analytics.userGrowthData,
          engagementMetrics: analytics.engagementMetrics,
          lastUpdated: DateTime.now(),
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refreshes analytics data
  Future<void> refreshAnalytics() async {
    await loadAnalytics();
  }
}

/// [BulkOperationsNotifier] manages the state for bulk user operations.
///
/// Provides:
/// - User selection for bulk operations
/// - Bulk status updates
/// - Bulk role updates
/// - Bulk deletion
///
/// Depends on:
/// - UserRepository for bulk operations
@riverpod
class BulkOperationsNotifier extends _$BulkOperationsNotifier {
  @override
  AsyncValue<BulkOperationsState> build() {
    return const AsyncValue.data(BulkOperationsState());
  }

  /// Selects users for bulk operations
  Future<void> selectUsers(List<String> userIds) async {
    final currentState = state.value ?? const BulkOperationsState();
    state = AsyncValue.data(
      currentState.copyWith(selectedUserIds: userIds),
    );
  }

  /// Adds a user to the selection
  Future<void> addUserToSelection(String userId) async {
    final currentState = state.value ?? const BulkOperationsState();
    if (!currentState.selectedUserIds.contains(userId)) {
      state = AsyncValue.data(
        currentState.copyWith(
          selectedUserIds: [...currentState.selectedUserIds, userId],
        ),
      );
    }
  }

  /// Removes a user from the selection
  Future<void> removeUserFromSelection(String userId) async {
    final currentState = state.value ?? const BulkOperationsState();
    state = AsyncValue.data(
      currentState.copyWith(
        selectedUserIds: currentState.selectedUserIds
            .where((id) => id != userId)
            .toList(),
      ),
    );
  }

  /// Clears the user selection
  Future<void> clearSelection() async {
    state = const AsyncValue.data(BulkOperationsState());
  }

  /// Performs bulk status update
  Future<void> bulkUpdateStatus(bool isActive) async {
    final currentState = state.value ?? const BulkOperationsState();
    
    if (currentState.selectedUserIds.isEmpty) return;

    try {
      state = AsyncValue.data(
        currentState.copyWith(isProcessing: true),
      );

      final repository = ref.read(userRepositoryProvider);
      await repository.bulkUpdateUserStatus(
        currentState.selectedUserIds,
        isActive,
      );

      state = AsyncValue.data(
        const BulkOperationsState().copyWith(
          successMessage: 'Successfully updated ${currentState.selectedUserIds.length} users',
        ),
      );

      // Refresh user list
      ref.read(userManagementNotifierProvider.notifier).loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Performs bulk role update
  Future<void> bulkUpdateRole(String role) async {
    final currentState = state.value ?? const BulkOperationsState();
    
    if (currentState.selectedUserIds.isEmpty) return;

    try {
      state = AsyncValue.data(
        currentState.copyWith(isProcessing: true),
      );

      final repository = ref.read(userRepositoryProvider);
      await repository.bulkUpdateUserRole(
        currentState.selectedUserIds,
        role,
      );

      state = AsyncValue.data(
        const BulkOperationsState().copyWith(
          successMessage: 'Successfully updated ${currentState.selectedUserIds.length} users',
        ),
      );

      // Refresh user list
      ref.read(userManagementNotifierProvider.notifier).loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Performs bulk deletion
  Future<void> bulkDeleteUsers() async {
    final currentState = state.value ?? const BulkOperationsState();
    
    if (currentState.selectedUserIds.isEmpty) return;

    try {
      state = AsyncValue.data(
        currentState.copyWith(isProcessing: true),
      );

      final repository = ref.read(userRepositoryProvider);
      await repository.bulkDeleteUsers(currentState.selectedUserIds);

      state = AsyncValue.data(
        const BulkOperationsState().copyWith(
          successMessage: 'Successfully deleted ${currentState.selectedUserIds.length} users',
        ),
      );

      // Refresh user list
      ref.read(userManagementNotifierProvider.notifier).loadUsers();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
