import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../core/services/supabase_service.dart';
import '../../../../shared/providers/database_providers.dart';
import '../../../../core/utils/validators.dart';

part 'auth_providers.g.dart';

/// State class for admin authentication
class AdminAuthState {
  final User? user;
  final bool isAuthenticated;
  final bool isLoading;
  final String? errorMessage;
  final String? role;
  final List<String> permissions;

  const AdminAuthState({
    this.user,
    this.isAuthenticated = false,
    this.isLoading = false,
    this.errorMessage,
    this.role,
    this.permissions = const [],
  });

  AdminAuthState copyWith({
    User? user,
    bool? isAuthenticated,
    bool? isLoading,
    String? errorMessage,
    String? role,
    List<String>? permissions,
  }) {
    return AdminAuthState(
      user: user ?? this.user,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
    );
  }
}

/// State class for login form
class LoginFormState {
  final String email;
  final String password;
  final bool isLoading;
  final bool isValid;
  final String? emailError;
  final String? passwordError;
  final String? errorMessage;

  const LoginFormState({
    this.email = '',
    this.password = '',
    this.isLoading = false,
    this.isValid = false,
    this.emailError,
    this.passwordError,
    this.errorMessage,
  });

  LoginFormState copyWith({
    String? email,
    String? password,
    bool? isLoading,
    bool? isValid,
    String? emailError,
    String? passwordError,
    String? errorMessage,
  }) {
    return LoginFormState(
      email: email ?? this.email,
      password: password ?? this.password,
      isLoading: isLoading ?? this.isLoading,
      isValid: isValid ?? this.isValid,
      emailError: emailError ?? this.emailError,
      passwordError: passwordError ?? this.passwordError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// [AdminAuthNotifier] manages the authentication state for admin users.
///
/// Provides:
/// - Admin authentication workflows
/// - Permission validation
/// - Session management
/// - Role-based access control
///
/// Depends on:
/// - SupabaseService for authentication operations
@riverpod
class AdminAuthNotifier extends _$AdminAuthNotifier {
  @override
  AsyncValue<AdminAuthState> build() {
    // Check for existing session on initialization
    _checkExistingSession();
    return const AsyncValue.data(AdminAuthState());
  }

  /// Checks for an existing authentication session
  void _checkExistingSession() {
    final supabaseService = ref.read(supabaseServiceProvider);
    final currentUser = supabaseService.getCurrentUser();

    if (currentUser != null && supabaseService.hasAdminPermissions()) {
      state = AsyncValue.data(
        AdminAuthState(
          user: currentUser,
          isAuthenticated: true,
          role: supabaseService.getUserRole(),
          permissions: supabaseService.getUserPermissions(),
        ),
      );
    }
  }

  /// Signs in an admin user with email and password
  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();

    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final authResult = await supabaseService.signInWithEmail(email, password);

      await authResult.when(
        success: (authResponse) async {
          if (authResponse.user == null) {
            throw Exception('Authentication failed');
          }

          // Verify admin permissions
          if (!supabaseService.hasAdminPermissions()) {
            await supabaseService.signOut();
            throw Exception('User does not have admin permissions');
          }

          state = AsyncValue.data(
            AdminAuthState(
              user: authResponse.user,
              isAuthenticated: true,
              role: supabaseService.getUserRole(),
              permissions: supabaseService.getUserPermissions(),
            ),
          );
        },
        failure: (error) async {
          throw Exception(error.message);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Signs out the current admin user
  Future<void> signOut() async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final signOutResult = await supabaseService.signOut();

      signOutResult.when(
        success: (_) {
          state = const AsyncValue.data(AdminAuthState());
        },
        failure: (error) {
          throw Exception(error.message);
        },
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refreshes the current user's session and permissions
  Future<void> refreshSession() async {
    try {
      final supabaseService = ref.read(supabaseServiceProvider);
      final currentUser = supabaseService.getCurrentUser();

      if (currentUser != null && supabaseService.hasAdminPermissions()) {
        state = AsyncValue.data(
          AdminAuthState(
            user: currentUser,
            isAuthenticated: true,
            role: supabaseService.getUserRole(),
            permissions: supabaseService.getUserPermissions(),
          ),
        );
      } else {
        state = const AsyncValue.data(AdminAuthState());
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Checks if the current user has a specific permission
  bool hasPermission(String permission) {
    final currentState = state.value;
    return currentState?.permissions.contains(permission) ?? false;
  }

  /// Checks if the current user has a specific role
  bool hasRole(String role) {
    final currentState = state.value;
    return currentState?.role == role;
  }

  /// Checks if the current user is a super admin
  bool isSuperAdmin() {
    return hasRole('super_admin');
  }

  /// Checks if the current user is an admin (admin or super_admin)
  bool isAdmin() {
    final currentState = state.value;
    return currentState?.role == 'admin' || currentState?.role == 'super_admin';
  }
}

/// [LoginFormNotifier] manages the state for the admin login form.
///
/// Provides:
/// - Form field validation
/// - Form submission handling
/// - Error state management
/// - Input sanitization
///
/// Depends on:
/// - AdminAuthNotifier for authentication
/// - Validators for input validation
@riverpod
class LoginFormNotifier extends _$LoginFormNotifier {
  @override
  AsyncValue<LoginFormState> build() {
    return const AsyncValue.data(LoginFormState());
  }

  /// Updates the email field and validates it
  Future<void> updateEmail(String email) async {
    final currentState = state.value ?? const LoginFormState();
    final emailError = Validators.email(email);

    final newState = currentState.copyWith(
      email: email,
      emailError: emailError,
      isValid: emailError == null && currentState.passwordError == null,
      errorMessage: null, // Clear any previous error
    );

    state = AsyncValue.data(newState);
  }

  /// Updates the password field and validates it
  Future<void> updatePassword(String password) async {
    final currentState = state.value ?? const LoginFormState();
    final passwordError = Validators.password(password);

    final newState = currentState.copyWith(
      password: password,
      passwordError: passwordError,
      isValid: passwordError == null && currentState.emailError == null,
      errorMessage: null, // Clear any previous error
    );

    state = AsyncValue.data(newState);
  }

  /// Submits the login form
  Future<void> submit() async {
    final currentState = state.value ?? const LoginFormState();

    if (!currentState.isValid) {
      state = AsyncValue.data(
        currentState.copyWith(
          errorMessage: 'Please fix the form errors before submitting',
        ),
      );
      return;
    }

    state = AsyncValue.data(
      currentState.copyWith(isLoading: true, errorMessage: null),
    );

    try {
      await ref.read(adminAuthNotifierProvider.notifier).signIn(
            currentState.email,
            currentState.password,
          );

      // Check if authentication was successful
      final authState = ref.read(adminAuthNotifierProvider);
      if (authState.hasError) {
        state = AsyncValue.data(
          currentState.copyWith(
            isLoading: false,
            errorMessage: authState.error.toString(),
          ),
        );
      } else {
        state = AsyncValue.data(
          currentState.copyWith(isLoading: false),
        );
      }
    } catch (error) {
      state = AsyncValue.data(
        currentState.copyWith(
          isLoading: false,
          errorMessage: error.toString(),
        ),
      );
    }
  }

  /// Clears the form
  Future<void> clear() async {
    state = const AsyncValue.data(LoginFormState());
  }

  /// Sets a general form error
  Future<void> setError(String errorMessage) async {
    final currentState = state.value ?? const LoginFormState();
    state = AsyncValue.data(
      currentState.copyWith(
        errorMessage: errorMessage,
        isLoading: false,
      ),
    );
  }

  /// Validates the entire form
  bool _validateForm(String email, String password) {
    final emailError = Validators.email(email);
    final passwordError = Validators.password(password);
    return emailError == null && passwordError == null;
  }
}

/// Provider for checking if user is authenticated
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(adminAuthNotifierProvider);
  return authState.value?.isAuthenticated ?? false;
}

/// Provider for getting current user
@riverpod
User? currentAdminUser(CurrentAdminUserRef ref) {
  final authState = ref.watch(adminAuthNotifierProvider);
  return authState.value?.user;
}

/// Provider for checking admin permissions
@riverpod
bool hasAdminAccess(HasAdminAccessRef ref) {
  final authState = ref.watch(adminAuthNotifierProvider);
  return authState.value?.isAuthenticated ?? false;
}

/// Provider for getting user role
@riverpod
String? userRole(UserRoleRef ref) {
  final authState = ref.watch(adminAuthNotifierProvider);
  return authState.value?.role;
}

/// Provider for getting user permissions
@riverpod
List<String> userPermissions(UserPermissionsRef ref) {
  final authState = ref.watch(adminAuthNotifierProvider);
  return authState.value?.permissions ?? [];
}
