// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$isAuthenticatedHash() => r'3de89e38c7bc19b93b1b6ac84a2dd8713a7da2d0';

/// Provider for checking if user is authenticated
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$currentAdminUserHash() => r'46a58f21b5379ba3c4e0912317f5d9328e4f6aa5';

/// Provider for getting current user
///
/// Copied from [currentAdminUser].
@ProviderFor(currentAdminUser)
final currentAdminUserProvider = AutoDisposeProvider<User?>.internal(
  currentAdminUser,
  name: r'currentAdminUserProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAdminUserHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentAdminUserRef = AutoDisposeProviderRef<User?>;
String _$hasAdminAccessHash() => r'f0b778a13208c1a878f0c31d9a9582fd06cd6a66';

/// Provider for checking admin permissions
///
/// Copied from [hasAdminAccess].
@ProviderFor(hasAdminAccess)
final hasAdminAccessProvider = AutoDisposeProvider<bool>.internal(
  hasAdminAccess,
  name: r'hasAdminAccessProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasAdminAccessHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HasAdminAccessRef = AutoDisposeProviderRef<bool>;
String _$userRoleHash() => r'6e07cbe682c0f31f17916fe4e219f821b0bf3377';

/// Provider for getting user role
///
/// Copied from [userRole].
@ProviderFor(userRole)
final userRoleProvider = AutoDisposeProvider<String?>.internal(
  userRole,
  name: r'userRoleProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userRoleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserRoleRef = AutoDisposeProviderRef<String?>;
String _$userPermissionsHash() => r'e1e36119f0ba6522d6e7c9b824a979c50518a42a';

/// Provider for getting user permissions
///
/// Copied from [userPermissions].
@ProviderFor(userPermissions)
final userPermissionsProvider = AutoDisposeProvider<List<String>>.internal(
  userPermissions,
  name: r'userPermissionsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userPermissionsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserPermissionsRef = AutoDisposeProviderRef<List<String>>;
String _$adminAuthNotifierHash() => r'9d3bdd7281ce36ad093a017b05ec66e43e6ab153';

/// [AdminAuthNotifier] manages the authentication state for admin users.
///
/// Provides:
/// - Admin authentication workflows
/// - Permission validation
/// - Session management
/// - Role-based access control
///
/// Depends on:
/// - SupabaseService for authentication operations
///
/// Copied from [AdminAuthNotifier].
@ProviderFor(AdminAuthNotifier)
final adminAuthNotifierProvider = AutoDisposeNotifierProvider<AdminAuthNotifier,
    AsyncValue<AdminAuthState>>.internal(
  AdminAuthNotifier.new,
  name: r'adminAuthNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminAuthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AdminAuthNotifier = AutoDisposeNotifier<AsyncValue<AdminAuthState>>;
String _$loginFormNotifierHash() => r'eb3dac1b5ff4f5aabd803ee1fa1b69563cdc1345';

/// [LoginFormNotifier] manages the state for the admin login form.
///
/// Provides:
/// - Form field validation
/// - Form submission handling
/// - Error state management
/// - Input sanitization
///
/// Depends on:
/// - AdminAuthNotifier for authentication
/// - Validators for input validation
///
/// Copied from [LoginFormNotifier].
@ProviderFor(LoginFormNotifier)
final loginFormNotifierProvider = AutoDisposeNotifierProvider<LoginFormNotifier,
    AsyncValue<LoginFormState>>.internal(
  LoginFormNotifier.new,
  name: r'loginFormNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$loginFormNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LoginFormNotifier = AutoDisposeNotifier<AsyncValue<LoginFormState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
