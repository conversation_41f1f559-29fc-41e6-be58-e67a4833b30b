// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journey_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JourneyEntityImpl _$$JourneyEntityImplFromJson(Map<String, dynamic> json) =>
    _$JourneyEntityImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      prompts: (json['prompts'] as List<dynamic>)
          .map((e) => PromptEntity.fromJson(e as Map<String, dynamic>))
          .toList(),
      category: json['category'] as String?,
      difficulty: json['difficulty'] as String? ?? 'beginner',
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt() ?? 15,
      isPublished: json['isPublished'] as bool? ?? false,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
    );

Map<String, dynamic> _$$JourneyEntityImplToJson(_$JourneyEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'prompts': instance.prompts,
      'category': instance.category,
      'difficulty': instance.difficulty,
      'estimatedDuration': instance.estimatedDuration,
      'isPublished': instance.isPublished,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
    };
