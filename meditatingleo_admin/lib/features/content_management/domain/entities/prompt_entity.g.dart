// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prompt_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PromptEntityImpl _$$PromptEntityImplFromJson(Map<String, dynamic> json) =>
    _$PromptEntityImpl(
      id: json['id'] as String,
      text: json['text'] as String,
      explanation: json['explanation'] as String?,
      example: json['example'] as String?,
      order: (json['order'] as num?)?.toInt() ?? 0,
      category: json['category'] as String?,
      isRequired: json['isRequired'] as bool? ?? true,
      estimatedTime: (json['estimatedTime'] as num?)?.toInt() ?? 5,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      createdBy: json['createdBy'] as String?,
    );

Map<String, dynamic> _$$PromptEntityImplToJson(_$PromptEntityImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'explanation': instance.explanation,
      'example': instance.example,
      'order': instance.order,
      'category': instance.category,
      'isRequired': instance.isRequired,
      'estimatedTime': instance.estimatedTime,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'createdBy': instance.createdBy,
    };
