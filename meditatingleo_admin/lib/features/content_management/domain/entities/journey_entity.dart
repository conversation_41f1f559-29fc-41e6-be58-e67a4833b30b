import 'package:freezed_annotation/freezed_annotation.dart';
import 'prompt_entity.dart';

part 'journey_entity.freezed.dart';
part 'journey_entity.g.dart';

/// [JourneyEntity] represents a clarity journal journey in the admin system.
///
/// A journey is a structured collection of prompts that guide users through
/// a specific clarity-building experience. This entity is used for content
/// management and creation in the admin panel.
///
/// Features:
/// - Immutable data structure using Freezed
/// - JSON serialization support
/// - Hierarchical prompt organization
/// - Metadata tracking (creation, updates)
/// - Content categorization
@freezed
class JourneyEntity with _$JourneyEntity {
  /// Creates a [JourneyEntity] instance.
  ///
  /// Parameters:
  /// - [id]: Unique identifier for the journey
  /// - [title]: Display title of the journey
  /// - [description]: Detailed description of the journey's purpose
  /// - [prompts]: List of prompts that make up this journey
  /// - [category]: Optional category for organization
  /// - [difficulty]: Difficulty level (beginner, intermediate, advanced)
  /// - [estimatedDuration]: Estimated completion time in minutes
  /// - [isPublished]: Whether the journey is published and available to users
  /// - [tags]: List of tags for searchability
  /// - [createdAt]: When the journey was created
  /// - [updatedAt]: When the journey was last modified
  /// - [createdBy]: ID of the admin user who created this journey
  const factory JourneyEntity({
    required String id,
    required String title,
    required String description,
    required List<PromptEntity> prompts,
    String? category,
    @Default('beginner') String difficulty,
    @Default(15) int estimatedDuration,
    @Default(false) bool isPublished,
    @Default([]) List<String> tags,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? createdBy,
  }) = _JourneyEntity;

  /// Creates a [JourneyEntity] from JSON.
  factory JourneyEntity.fromJson(Map<String, dynamic> json) =>
      _$JourneyEntityFromJson(json);
}

/// Extension methods for [JourneyEntity] to provide additional functionality
extension JourneyEntityExtensions on JourneyEntity {
  /// Returns true if the journey has any prompts
  bool get hasPrompts => prompts.isNotEmpty;

  /// Returns the number of prompts in this journey
  int get promptCount => prompts.length;

  /// Returns true if the journey is ready for publishing
  /// (has title, description, and at least one prompt)
  bool get isReadyForPublishing =>
      title.isNotEmpty && description.isNotEmpty && hasPrompts;

  /// Returns a copy of the journey with a new prompt added
  JourneyEntity addPrompt(PromptEntity prompt) {
    return copyWith(
      prompts: [...prompts, prompt],
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the journey with a prompt removed
  JourneyEntity removePrompt(String promptId) {
    return copyWith(
      prompts: prompts.where((p) => p.id != promptId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the journey with prompts reordered
  JourneyEntity reorderPrompts(List<PromptEntity> reorderedPrompts) {
    return copyWith(
      prompts: reorderedPrompts,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the journey marked as published
  JourneyEntity publish() {
    return copyWith(
      isPublished: true,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the journey marked as unpublished
  JourneyEntity unpublish() {
    return copyWith(
      isPublished: false,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a summary string for display purposes
  String get summary =>
      '$title ($promptCount prompts, ${estimatedDuration}min, $difficulty)';
}
