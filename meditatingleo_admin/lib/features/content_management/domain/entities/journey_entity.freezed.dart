// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'journey_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JourneyEntity _$JourneyEntityFromJson(Map<String, dynamic> json) {
  return _JourneyEntity.fromJson(json);
}

/// @nodoc
mixin _$JourneyEntity {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  List<PromptEntity> get prompts => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  int get estimatedDuration => throw _privateConstructorUsedError;
  bool get isPublished => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyEntity value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyEntity value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyEntity value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this JourneyEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JourneyEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JourneyEntityCopyWith<JourneyEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JourneyEntityCopyWith<$Res> {
  factory $JourneyEntityCopyWith(
          JourneyEntity value, $Res Function(JourneyEntity) then) =
      _$JourneyEntityCopyWithImpl<$Res, JourneyEntity>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      List<PromptEntity> prompts,
      String? category,
      String difficulty,
      int estimatedDuration,
      bool isPublished,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt,
      String? createdBy});
}

/// @nodoc
class _$JourneyEntityCopyWithImpl<$Res, $Val extends JourneyEntity>
    implements $JourneyEntityCopyWith<$Res> {
  _$JourneyEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JourneyEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? prompts = null,
    Object? category = freezed,
    Object? difficulty = null,
    Object? estimatedDuration = null,
    Object? isPublished = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value.prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<PromptEntity>,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedDuration: null == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JourneyEntityImplCopyWith<$Res>
    implements $JourneyEntityCopyWith<$Res> {
  factory _$$JourneyEntityImplCopyWith(
          _$JourneyEntityImpl value, $Res Function(_$JourneyEntityImpl) then) =
      __$$JourneyEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      List<PromptEntity> prompts,
      String? category,
      String difficulty,
      int estimatedDuration,
      bool isPublished,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt,
      String? createdBy});
}

/// @nodoc
class __$$JourneyEntityImplCopyWithImpl<$Res>
    extends _$JourneyEntityCopyWithImpl<$Res, _$JourneyEntityImpl>
    implements _$$JourneyEntityImplCopyWith<$Res> {
  __$$JourneyEntityImplCopyWithImpl(
      _$JourneyEntityImpl _value, $Res Function(_$JourneyEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of JourneyEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? prompts = null,
    Object? category = freezed,
    Object? difficulty = null,
    Object? estimatedDuration = null,
    Object? isPublished = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = freezed,
  }) {
    return _then(_$JourneyEntityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value._prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<PromptEntity>,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedDuration: null == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JourneyEntityImpl implements _JourneyEntity {
  const _$JourneyEntityImpl(
      {required this.id,
      required this.title,
      required this.description,
      required final List<PromptEntity> prompts,
      this.category,
      this.difficulty = 'beginner',
      this.estimatedDuration = 15,
      this.isPublished = false,
      final List<String> tags = const [],
      required this.createdAt,
      required this.updatedAt,
      this.createdBy})
      : _prompts = prompts,
        _tags = tags;

  factory _$JourneyEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$JourneyEntityImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  final List<PromptEntity> _prompts;
  @override
  List<PromptEntity> get prompts {
    if (_prompts is EqualUnmodifiableListView) return _prompts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prompts);
  }

  @override
  final String? category;
  @override
  @JsonKey()
  final String difficulty;
  @override
  @JsonKey()
  final int estimatedDuration;
  @override
  @JsonKey()
  final bool isPublished;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final String? createdBy;

  @override
  String toString() {
    return 'JourneyEntity(id: $id, title: $title, description: $description, prompts: $prompts, category: $category, difficulty: $difficulty, estimatedDuration: $estimatedDuration, isPublished: $isPublished, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JourneyEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._prompts, _prompts) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            (identical(other.isPublished, isPublished) ||
                other.isPublished == isPublished) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      const DeepCollectionEquality().hash(_prompts),
      category,
      difficulty,
      estimatedDuration,
      isPublished,
      const DeepCollectionEquality().hash(_tags),
      createdAt,
      updatedAt,
      createdBy);

  /// Create a copy of JourneyEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JourneyEntityImplCopyWith<_$JourneyEntityImpl> get copyWith =>
      __$$JourneyEntityImplCopyWithImpl<_$JourneyEntityImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyEntity value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyEntity value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyEntity value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$JourneyEntityImplToJson(
      this,
    );
  }
}

abstract class _JourneyEntity implements JourneyEntity {
  const factory _JourneyEntity(
      {required final String id,
      required final String title,
      required final String description,
      required final List<PromptEntity> prompts,
      final String? category,
      final String difficulty,
      final int estimatedDuration,
      final bool isPublished,
      final List<String> tags,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final String? createdBy}) = _$JourneyEntityImpl;

  factory _JourneyEntity.fromJson(Map<String, dynamic> json) =
      _$JourneyEntityImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  List<PromptEntity> get prompts;
  @override
  String? get category;
  @override
  String get difficulty;
  @override
  int get estimatedDuration;
  @override
  bool get isPublished;
  @override
  List<String> get tags;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  String? get createdBy;

  /// Create a copy of JourneyEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JourneyEntityImplCopyWith<_$JourneyEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
