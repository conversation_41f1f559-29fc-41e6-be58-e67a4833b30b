import 'package:freezed_annotation/freezed_annotation.dart';

part 'prompt_entity.freezed.dart';
part 'prompt_entity.g.dart';

/// [PromptEntity] represents a single clarity journal prompt in the admin system.
///
/// A prompt is an individual question or reflection point that guides users
/// in their clarity journey. This entity is used for content creation and
/// management in the admin panel.
///
/// Features:
/// - Immutable data structure using Freezed
/// - JSON serialization support
/// - Rich content support (text, examples, explanations)
/// - Ordering and categorization
/// - Metadata tracking
@freezed
class PromptEntity with _$PromptEntity {
  /// Creates a [PromptEntity] instance.
  ///
  /// Parameters:
  /// - [id]: Unique identifier for the prompt
  /// - [text]: The main prompt text/question
  /// - [explanation]: Optional explanation or guidance for the prompt
  /// - [example]: Optional example response to help users understand
  /// - [order]: Position of this prompt within its journey
  /// - [category]: Optional category for organization
  /// - [isRequired]: Whether this prompt must be answered to continue
  /// - [estimatedTime]: Estimated time to complete this prompt in minutes
  /// - [tags]: List of tags for searchability and organization
  /// - [createdAt]: When the prompt was created
  /// - [updatedAt]: When the prompt was last modified
  /// - [createdBy]: ID of the admin user who created this prompt
  const factory PromptEntity({
    required String id,
    required String text,
    String? explanation,
    String? example,
    @Default(0) int order,
    String? category,
    @Default(true) bool isRequired,
    @Default(5) int estimatedTime,
    @Default([]) List<String> tags,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? createdBy,
  }) = _PromptEntity;

  /// Creates a [PromptEntity] from JSON.
  factory PromptEntity.fromJson(Map<String, dynamic> json) =>
      _$PromptEntityFromJson(json);
}

/// Extension methods for [PromptEntity] to provide additional functionality
extension PromptEntityExtensions on PromptEntity {
  /// Returns true if the prompt has explanation text
  bool get hasExplanation => explanation != null && explanation!.isNotEmpty;

  /// Returns true if the prompt has an example response
  bool get hasExample => example != null && example!.isNotEmpty;

  /// Returns true if the prompt is complete (has text and is properly configured)
  bool get isComplete => text.isNotEmpty;

  /// Returns true if the prompt has rich content (explanation or example)
  bool get hasRichContent => hasExplanation || hasExample;

  /// Returns a copy of the prompt with updated text
  PromptEntity updateText(String newText) {
    return copyWith(
      text: newText,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with updated explanation
  PromptEntity updateExplanation(String? newExplanation) {
    return copyWith(
      explanation: newExplanation,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with updated example
  PromptEntity updateExample(String? newExample) {
    return copyWith(
      example: newExample,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with updated order
  PromptEntity updateOrder(int newOrder) {
    return copyWith(
      order: newOrder,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with updated category
  PromptEntity updateCategory(String? newCategory) {
    return copyWith(
      category: newCategory,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with updated tags
  PromptEntity updateTags(List<String> newTags) {
    return copyWith(
      tags: newTags,
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with a tag added
  PromptEntity addTag(String tag) {
    if (tags.contains(tag)) return this;
    return copyWith(
      tags: [...tags, tag],
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a copy of the prompt with a tag removed
  PromptEntity removeTag(String tag) {
    return copyWith(
      tags: tags.where((t) => t != tag).toList(),
      updatedAt: DateTime.now(),
    );
  }

  /// Returns a summary string for display purposes
  String get summary {
    final parts = <String>[text];
    if (hasExplanation) parts.add('with explanation');
    if (hasExample) parts.add('with example');
    if (!isRequired) parts.add('optional');
    return parts.join(' ');
  }

  /// Returns the display text truncated to a specific length
  String getDisplayText([int maxLength = 100]) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
}
