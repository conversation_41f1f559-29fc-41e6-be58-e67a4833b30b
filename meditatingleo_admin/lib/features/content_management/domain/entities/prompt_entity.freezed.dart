// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'prompt_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PromptEntity _$PromptEntityFromJson(Map<String, dynamic> json) {
  return _PromptEntity.fromJson(json);
}

/// @nodoc
mixin _$PromptEntity {
  String get id => throw _privateConstructorUsedError;
  String get text => throw _privateConstructorUsedError;
  String? get explanation => throw _privateConstructorUsedError;
  String? get example => throw _privateConstructorUsedError;
  int get order => throw _privateConstructorUsedError;
  String? get category => throw _privateConstructorUsedError;
  bool get isRequired => throw _privateConstructorUsedError;
  int get estimatedTime => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PromptEntity value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PromptEntity value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PromptEntity value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this PromptEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PromptEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PromptEntityCopyWith<PromptEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PromptEntityCopyWith<$Res> {
  factory $PromptEntityCopyWith(
          PromptEntity value, $Res Function(PromptEntity) then) =
      _$PromptEntityCopyWithImpl<$Res, PromptEntity>;
  @useResult
  $Res call(
      {String id,
      String text,
      String? explanation,
      String? example,
      int order,
      String? category,
      bool isRequired,
      int estimatedTime,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt,
      String? createdBy});
}

/// @nodoc
class _$PromptEntityCopyWithImpl<$Res, $Val extends PromptEntity>
    implements $PromptEntityCopyWith<$Res> {
  _$PromptEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PromptEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? explanation = freezed,
    Object? example = freezed,
    Object? order = null,
    Object? category = freezed,
    Object? isRequired = null,
    Object? estimatedTime = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: freezed == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String?,
      example: freezed == example
          ? _value.example
          : example // ignore: cast_nullable_to_non_nullable
              as String?,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      isRequired: null == isRequired
          ? _value.isRequired
          : isRequired // ignore: cast_nullable_to_non_nullable
              as bool,
      estimatedTime: null == estimatedTime
          ? _value.estimatedTime
          : estimatedTime // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PromptEntityImplCopyWith<$Res>
    implements $PromptEntityCopyWith<$Res> {
  factory _$$PromptEntityImplCopyWith(
          _$PromptEntityImpl value, $Res Function(_$PromptEntityImpl) then) =
      __$$PromptEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String text,
      String? explanation,
      String? example,
      int order,
      String? category,
      bool isRequired,
      int estimatedTime,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt,
      String? createdBy});
}

/// @nodoc
class __$$PromptEntityImplCopyWithImpl<$Res>
    extends _$PromptEntityCopyWithImpl<$Res, _$PromptEntityImpl>
    implements _$$PromptEntityImplCopyWith<$Res> {
  __$$PromptEntityImplCopyWithImpl(
      _$PromptEntityImpl _value, $Res Function(_$PromptEntityImpl) _then)
      : super(_value, _then);

  /// Create a copy of PromptEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? explanation = freezed,
    Object? example = freezed,
    Object? order = null,
    Object? category = freezed,
    Object? isRequired = null,
    Object? estimatedTime = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? createdBy = freezed,
  }) {
    return _then(_$PromptEntityImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: freezed == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String?,
      example: freezed == example
          ? _value.example
          : example // ignore: cast_nullable_to_non_nullable
              as String?,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String?,
      isRequired: null == isRequired
          ? _value.isRequired
          : isRequired // ignore: cast_nullable_to_non_nullable
              as bool,
      estimatedTime: null == estimatedTime
          ? _value.estimatedTime
          : estimatedTime // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PromptEntityImpl implements _PromptEntity {
  const _$PromptEntityImpl(
      {required this.id,
      required this.text,
      this.explanation,
      this.example,
      this.order = 0,
      this.category,
      this.isRequired = true,
      this.estimatedTime = 5,
      final List<String> tags = const [],
      required this.createdAt,
      required this.updatedAt,
      this.createdBy})
      : _tags = tags;

  factory _$PromptEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$PromptEntityImplFromJson(json);

  @override
  final String id;
  @override
  final String text;
  @override
  final String? explanation;
  @override
  final String? example;
  @override
  @JsonKey()
  final int order;
  @override
  final String? category;
  @override
  @JsonKey()
  final bool isRequired;
  @override
  @JsonKey()
  final int estimatedTime;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final String? createdBy;

  @override
  String toString() {
    return 'PromptEntity(id: $id, text: $text, explanation: $explanation, example: $example, order: $order, category: $category, isRequired: $isRequired, estimatedTime: $estimatedTime, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PromptEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.explanation, explanation) ||
                other.explanation == explanation) &&
            (identical(other.example, example) || other.example == example) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.isRequired, isRequired) ||
                other.isRequired == isRequired) &&
            (identical(other.estimatedTime, estimatedTime) ||
                other.estimatedTime == estimatedTime) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      text,
      explanation,
      example,
      order,
      category,
      isRequired,
      estimatedTime,
      const DeepCollectionEquality().hash(_tags),
      createdAt,
      updatedAt,
      createdBy);

  /// Create a copy of PromptEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PromptEntityImplCopyWith<_$PromptEntityImpl> get copyWith =>
      __$$PromptEntityImplCopyWithImpl<_$PromptEntityImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PromptEntity value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PromptEntity value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PromptEntity value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$PromptEntityImplToJson(
      this,
    );
  }
}

abstract class _PromptEntity implements PromptEntity {
  const factory _PromptEntity(
      {required final String id,
      required final String text,
      final String? explanation,
      final String? example,
      final int order,
      final String? category,
      final bool isRequired,
      final int estimatedTime,
      final List<String> tags,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final String? createdBy}) = _$PromptEntityImpl;

  factory _PromptEntity.fromJson(Map<String, dynamic> json) =
      _$PromptEntityImpl.fromJson;

  @override
  String get id;
  @override
  String get text;
  @override
  String? get explanation;
  @override
  String? get example;
  @override
  int get order;
  @override
  String? get category;
  @override
  bool get isRequired;
  @override
  int get estimatedTime;
  @override
  List<String> get tags;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  String? get createdBy;

  /// Create a copy of PromptEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PromptEntityImplCopyWith<_$PromptEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
