// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_management_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$contentManagementNotifierHash() =>
    r'cc3ce834d4cbda64657e245625664cfdda29aace';

/// [ContentManagementNotifier] manages the state for content management operations.
///
/// Provides:
/// - Journey loading and management
/// - Journey selection and filtering
/// - Content creation workflows
/// - Error handling for content operations
///
/// Depends on:
/// - ContentRepository for data operations
///
/// Copied from [ContentManagementNotifier].
@ProviderFor(ContentManagementNotifier)
final contentManagementNotifierProvider = AutoDisposeNotifierProvider<
    ContentManagementNotifier, AsyncValue<ContentManagementState>>.internal(
  ContentManagementNotifier.new,
  name: r'contentManagementNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$contentManagementNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ContentManagementNotifier
    = AutoDisposeNotifier<AsyncValue<ContentManagementState>>;
String _$journeyBuilderNotifierHash() =>
    r'49afa4bbd335d33e6a4b650471b04fa9d5d97cc1';

/// [JourneyBuilderNotifier] manages the state for journey creation and editing.
///
/// Provides:
/// - Journey creation workflow
/// - Prompt management within journeys
/// - Draft state management
/// - Save and publish operations
///
/// Depends on:
/// - ContentRepository for data operations
///
/// Copied from [JourneyBuilderNotifier].
@ProviderFor(JourneyBuilderNotifier)
final journeyBuilderNotifierProvider = AutoDisposeNotifierProvider<
    JourneyBuilderNotifier, AsyncValue<JourneyBuilderState>>.internal(
  JourneyBuilderNotifier.new,
  name: r'journeyBuilderNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$journeyBuilderNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JourneyBuilderNotifier
    = AutoDisposeNotifier<AsyncValue<JourneyBuilderState>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
