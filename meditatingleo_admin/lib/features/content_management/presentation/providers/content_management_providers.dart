import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/journey_entity.dart';
import '../../domain/entities/prompt_entity.dart';
import '../../../../core/repositories/content_repository.dart';
import '../../../../shared/providers/repository_providers.dart';

part 'content_management_providers.g.dart';

/// State class for content management
class ContentManagementState {
  final List<JourneyEntity> journeys;
  final JourneyEntity? selectedJourney;
  final bool isLoading;
  final String? errorMessage;

  const ContentManagementState({
    this.journeys = const [],
    this.selectedJourney,
    this.isLoading = false,
    this.errorMessage,
  });

  ContentManagementState copyWith({
    List<JourneyEntity>? journeys,
    JourneyEntity? selectedJourney,
    bool? isLoading,
    String? errorMessage,
  }) {
    return ContentManagementState(
      journeys: journeys ?? this.journeys,
      selectedJourney: selectedJourney ?? this.selectedJourney,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// State class for journey builder
class JourneyBuilderState {
  final JourneyEntity? currentJourney;
  final List<PromptEntity> prompts;
  final bool isDirty;
  final bool isLoading;
  final String? errorMessage;

  const JourneyBuilderState({
    this.currentJourney,
    this.prompts = const [],
    this.isDirty = false,
    this.isLoading = false,
    this.errorMessage,
  });

  JourneyBuilderState copyWith({
    JourneyEntity? currentJourney,
    List<PromptEntity>? prompts,
    bool? isDirty,
    bool? isLoading,
    String? errorMessage,
  }) {
    return JourneyBuilderState(
      currentJourney: currentJourney ?? this.currentJourney,
      prompts: prompts ?? this.prompts,
      isDirty: isDirty ?? this.isDirty,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// [ContentManagementNotifier] manages the state for content management operations.
///
/// Provides:
/// - Journey loading and management
/// - Journey selection and filtering
/// - Content creation workflows
/// - Error handling for content operations
///
/// Depends on:
/// - ContentRepository for data operations
@riverpod
class ContentManagementNotifier extends _$ContentManagementNotifier {
  @override
  AsyncValue<ContentManagementState> build() {
    return const AsyncValue.data(ContentManagementState());
  }

  /// Loads all journeys from the repository
  Future<void> loadJourneys() async {
    state = const AsyncValue.loading();
    
    try {
      final repository = ref.read(contentRepositoryProvider);
      final journeys = await repository.getAllJourneys();
      
      state = AsyncValue.data(
        const ContentManagementState().copyWith(journeys: journeys),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Creates a new journey with the given title and description
  Future<void> createJourney(String title, String description) async {
    try {
      final repository = ref.read(contentRepositoryProvider);
      final newJourney = await repository.createJourney(
        JourneyEntity(
          id: const Uuid().v4(),
          title: title,
          description: description,
          prompts: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      // Reload journeys to include the new one
      await loadJourneys();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Selects a journey for detailed viewing/editing
  Future<void> selectJourney(JourneyEntity journey) async {
    final currentState = state.value ?? const ContentManagementState();
    state = AsyncValue.data(
      currentState.copyWith(selectedJourney: journey),
    );
  }

  /// Clears the selected journey
  Future<void> clearSelection() async {
    final currentState = state.value ?? const ContentManagementState();
    state = AsyncValue.data(
      currentState.copyWith(selectedJourney: null),
    );
  }

  /// Deletes a journey
  Future<void> deleteJourney(String journeyId) async {
    try {
      final repository = ref.read(contentRepositoryProvider);
      await repository.deleteJourney(journeyId);
      
      // Reload journeys to reflect the deletion
      await loadJourneys();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Updates an existing journey
  Future<void> updateJourney(JourneyEntity journey) async {
    try {
      final repository = ref.read(contentRepositoryProvider);
      await repository.updateJourney(journey);
      
      // Reload journeys to reflect the update
      await loadJourneys();
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// [JourneyBuilderNotifier] manages the state for journey creation and editing.
///
/// Provides:
/// - Journey creation workflow
/// - Prompt management within journeys
/// - Draft state management
/// - Save and publish operations
///
/// Depends on:
/// - ContentRepository for data operations
@riverpod
class JourneyBuilderNotifier extends _$JourneyBuilderNotifier {
  @override
  AsyncValue<JourneyBuilderState> build() {
    return const AsyncValue.data(JourneyBuilderState());
  }

  /// Starts creating a new journey
  Future<void> startNewJourney() async {
    final newJourney = JourneyEntity(
      id: const Uuid().v4(),
      title: '',
      description: '',
      prompts: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    state = AsyncValue.data(
      const JourneyBuilderState().copyWith(
        currentJourney: newJourney,
        prompts: [],
        isDirty: true,
      ),
    );
  }

  /// Loads an existing journey for editing
  Future<void> loadJourney(JourneyEntity journey) async {
    state = AsyncValue.data(
      const JourneyBuilderState().copyWith(
        currentJourney: journey,
        prompts: journey.prompts,
        isDirty: false,
      ),
    );
  }

  /// Adds a new prompt to the current journey
  Future<void> addPrompt(String text, String? explanation) async {
    final currentState = state.value ?? const JourneyBuilderState();
    
    final newPrompt = PromptEntity(
      id: const Uuid().v4(),
      text: text,
      explanation: explanation,
      order: currentState.prompts.length,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    state = AsyncValue.data(
      currentState.copyWith(
        prompts: [...currentState.prompts, newPrompt],
        isDirty: true,
      ),
    );
  }

  /// Removes a prompt from the current journey
  Future<void> removePrompt(String promptId) async {
    final currentState = state.value ?? const JourneyBuilderState();
    
    state = AsyncValue.data(
      currentState.copyWith(
        prompts: currentState.prompts.where((p) => p.id != promptId).toList(),
        isDirty: true,
      ),
    );
  }

  /// Updates a prompt in the current journey
  Future<void> updatePrompt(PromptEntity updatedPrompt) async {
    final currentState = state.value ?? const JourneyBuilderState();
    
    final updatedPrompts = currentState.prompts.map((prompt) {
      return prompt.id == updatedPrompt.id ? updatedPrompt : prompt;
    }).toList();

    state = AsyncValue.data(
      currentState.copyWith(
        prompts: updatedPrompts,
        isDirty: true,
      ),
    );
  }

  /// Saves the current journey
  Future<void> saveJourney(String title, String description) async {
    final currentState = state.value ?? const JourneyBuilderState();
    
    if (currentState.currentJourney == null) {
      throw Exception('No journey to save');
    }

    try {
      final repository = ref.read(contentRepositoryProvider);
      
      final journeyToSave = currentState.currentJourney!.copyWith(
        title: title,
        description: description,
        prompts: currentState.prompts,
        updatedAt: DateTime.now(),
      );

      await repository.createJourney(journeyToSave);

      state = AsyncValue.data(
        currentState.copyWith(
          currentJourney: journeyToSave,
          isDirty: false,
        ),
      );
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Discards changes and resets the builder
  Future<void> discardChanges() async {
    state = const AsyncValue.data(JourneyBuilderState());
  }
}
