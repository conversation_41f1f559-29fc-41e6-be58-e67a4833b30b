import 'package:freezed_annotation/freezed_annotation.dart';

part 'journey_content.freezed.dart';
part 'journey_content.g.dart';

/// Represents a clarity journal journey with prompts and metadata
@freezed
class JourneyContent with _$JourneyContent {
  const factory JourneyContent({
    required String id,
    required String title,
    required String description,
    required String category,
    required String difficulty,
    required List<String> prompts,
    @Default(false) bool isPublished,
    required DateTime createdAt,
    required DateTime updatedAt,
    String? imageUrl,
    int? estimatedDuration,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _JourneyContent;

  /// Create from JSON (Supabase response)
  factory JourneyContent.fromJson(Map<String, dynamic> json) {
    return JourneyContent(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      difficulty: json['difficulty'] as String,
      prompts: List<String>.from(json['prompts'] ?? []),
      isPublished: json['is_published'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      imageUrl: json['image_url'] as String?,
      estimatedDuration: json['estimated_duration'] as int?,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}

/// Journey content creation/update data
@freezed
class JourneyContentInput with _$JourneyContentInput {
  const factory JourneyContentInput({
    required String title,
    required String description,
    required String category,
    required String difficulty,
    required List<String> prompts,
    @Default(false) bool isPublished,
    String? imageUrl,
    int? estimatedDuration,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) = _JourneyContentInput;

  factory JourneyContentInput.fromJson(Map<String, dynamic> json) =>
      _$JourneyContentInputFromJson(json);
}

/// Journey content categories
enum JourneyCategory {
  mindfulness('mindfulness', 'Mindfulness'),
  gratitude('gratitude', 'Gratitude'),
  reflection('reflection', 'Reflection'),
  goals('goals', 'Goals'),
  emotions('emotions', 'Emotions'),
  relationships('relationships', 'Relationships'),
  growth('growth', 'Personal Growth'),
  creativity('creativity', 'Creativity'),
  stress('stress', 'Stress Management'),
  sleep('sleep', 'Sleep & Rest');

  const JourneyCategory(this.value, this.displayName);

  final String value;
  final String displayName;

  static JourneyCategory fromValue(String value) {
    return JourneyCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => JourneyCategory.mindfulness,
    );
  }
}

/// Journey difficulty levels
enum JourneyDifficulty {
  beginner('beginner', 'Beginner', 'Perfect for those new to journaling'),
  intermediate('intermediate', 'Intermediate',
      'For those with some journaling experience'),
  advanced('advanced', 'Advanced',
      'Deep, challenging prompts for experienced journalers');

  const JourneyDifficulty(this.value, this.displayName, this.description);

  final String value;
  final String displayName;
  final String description;

  static JourneyDifficulty fromValue(String value) {
    return JourneyDifficulty.values.firstWhere(
      (difficulty) => difficulty.value == value,
      orElse: () => JourneyDifficulty.beginner,
    );
  }
}

/// Journey content statistics
@freezed
class JourneyContentStats with _$JourneyContentStats {
  const factory JourneyContentStats({
    required int totalJourneys,
    required int publishedJourneys,
    required int draftJourneys,
    required Map<String, int> categoryCounts,
    required Map<String, int> difficultyCounts,
    required DateTime lastUpdated,
  }) = _JourneyContentStats;

  factory JourneyContentStats.fromJson(Map<String, dynamic> json) =>
      _$JourneyContentStatsFromJson(json);
}
