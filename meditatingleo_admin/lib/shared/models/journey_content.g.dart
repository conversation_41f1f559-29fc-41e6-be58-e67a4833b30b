// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journey_content.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JourneyContentInputImpl _$$JourneyContentInputImplFromJson(
        Map<String, dynamic> json) =>
    _$JourneyContentInputImpl(
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      difficulty: json['difficulty'] as String,
      prompts:
          (json['prompts'] as List<dynamic>).map((e) => e as String).toList(),
      isPublished: json['isPublished'] as bool? ?? false,
      imageUrl: json['imageUrl'] as String?,
      estimatedDuration: (json['estimatedDuration'] as num?)?.toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$JourneyContentInputImplToJson(
        _$JourneyContentInputImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'category': instance.category,
      'difficulty': instance.difficulty,
      'prompts': instance.prompts,
      'isPublished': instance.isPublished,
      'imageUrl': instance.imageUrl,
      'estimatedDuration': instance.estimatedDuration,
      'tags': instance.tags,
      'metadata': instance.metadata,
    };

_$JourneyContentStatsImpl _$$JourneyContentStatsImplFromJson(
        Map<String, dynamic> json) =>
    _$JourneyContentStatsImpl(
      totalJourneys: (json['totalJourneys'] as num).toInt(),
      publishedJourneys: (json['publishedJourneys'] as num).toInt(),
      draftJourneys: (json['draftJourneys'] as num).toInt(),
      categoryCounts: Map<String, int>.from(json['categoryCounts'] as Map),
      difficultyCounts: Map<String, int>.from(json['difficultyCounts'] as Map),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$JourneyContentStatsImplToJson(
        _$JourneyContentStatsImpl instance) =>
    <String, dynamic>{
      'totalJourneys': instance.totalJourneys,
      'publishedJourneys': instance.publishedJourneys,
      'draftJourneys': instance.draftJourneys,
      'categoryCounts': instance.categoryCounts,
      'difficultyCounts': instance.difficultyCounts,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
