// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'journey_content.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JourneyContent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  List<String> get prompts => throw _privateConstructorUsedError;
  bool get isPublished => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  int? get estimatedDuration => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContent value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContent value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContent value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of JourneyContent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JourneyContentCopyWith<JourneyContent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JourneyContentCopyWith<$Res> {
  factory $JourneyContentCopyWith(
          JourneyContent value, $Res Function(JourneyContent) then) =
      _$JourneyContentCopyWithImpl<$Res, JourneyContent>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String category,
      String difficulty,
      List<String> prompts,
      bool isPublished,
      DateTime createdAt,
      DateTime updatedAt,
      String? imageUrl,
      int? estimatedDuration,
      List<String>? tags,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$JourneyContentCopyWithImpl<$Res, $Val extends JourneyContent>
    implements $JourneyContentCopyWith<$Res> {
  _$JourneyContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JourneyContent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? difficulty = null,
    Object? prompts = null,
    Object? isPublished = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? imageUrl = freezed,
    Object? estimatedDuration = freezed,
    Object? tags = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value.prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JourneyContentImplCopyWith<$Res>
    implements $JourneyContentCopyWith<$Res> {
  factory _$$JourneyContentImplCopyWith(_$JourneyContentImpl value,
          $Res Function(_$JourneyContentImpl) then) =
      __$$JourneyContentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String category,
      String difficulty,
      List<String> prompts,
      bool isPublished,
      DateTime createdAt,
      DateTime updatedAt,
      String? imageUrl,
      int? estimatedDuration,
      List<String>? tags,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$JourneyContentImplCopyWithImpl<$Res>
    extends _$JourneyContentCopyWithImpl<$Res, _$JourneyContentImpl>
    implements _$$JourneyContentImplCopyWith<$Res> {
  __$$JourneyContentImplCopyWithImpl(
      _$JourneyContentImpl _value, $Res Function(_$JourneyContentImpl) _then)
      : super(_value, _then);

  /// Create a copy of JourneyContent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? difficulty = null,
    Object? prompts = null,
    Object? isPublished = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? imageUrl = freezed,
    Object? estimatedDuration = freezed,
    Object? tags = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$JourneyContentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value._prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$JourneyContentImpl implements _JourneyContent {
  const _$JourneyContentImpl(
      {required this.id,
      required this.title,
      required this.description,
      required this.category,
      required this.difficulty,
      required final List<String> prompts,
      this.isPublished = false,
      required this.createdAt,
      required this.updatedAt,
      this.imageUrl,
      this.estimatedDuration,
      final List<String>? tags,
      final Map<String, dynamic>? metadata})
      : _prompts = prompts,
        _tags = tags,
        _metadata = metadata;

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String category;
  @override
  final String difficulty;
  final List<String> _prompts;
  @override
  List<String> get prompts {
    if (_prompts is EqualUnmodifiableListView) return _prompts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prompts);
  }

  @override
  @JsonKey()
  final bool isPublished;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final String? imageUrl;
  @override
  final int? estimatedDuration;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'JourneyContent(id: $id, title: $title, description: $description, category: $category, difficulty: $difficulty, prompts: $prompts, isPublished: $isPublished, createdAt: $createdAt, updatedAt: $updatedAt, imageUrl: $imageUrl, estimatedDuration: $estimatedDuration, tags: $tags, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JourneyContentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            const DeepCollectionEquality().equals(other._prompts, _prompts) &&
            (identical(other.isPublished, isPublished) ||
                other.isPublished == isPublished) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      category,
      difficulty,
      const DeepCollectionEquality().hash(_prompts),
      isPublished,
      createdAt,
      updatedAt,
      imageUrl,
      estimatedDuration,
      const DeepCollectionEquality().hash(_tags),
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of JourneyContent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JourneyContentImplCopyWith<_$JourneyContentImpl> get copyWith =>
      __$$JourneyContentImplCopyWithImpl<_$JourneyContentImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContent value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContent value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContent value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _JourneyContent implements JourneyContent {
  const factory _JourneyContent(
      {required final String id,
      required final String title,
      required final String description,
      required final String category,
      required final String difficulty,
      required final List<String> prompts,
      final bool isPublished,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final String? imageUrl,
      final int? estimatedDuration,
      final List<String>? tags,
      final Map<String, dynamic>? metadata}) = _$JourneyContentImpl;

  @override
  String get id;
  @override
  String get title;
  @override
  String get description;
  @override
  String get category;
  @override
  String get difficulty;
  @override
  List<String> get prompts;
  @override
  bool get isPublished;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  String? get imageUrl;
  @override
  int? get estimatedDuration;
  @override
  List<String>? get tags;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of JourneyContent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JourneyContentImplCopyWith<_$JourneyContentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

JourneyContentInput _$JourneyContentInputFromJson(Map<String, dynamic> json) {
  return _JourneyContentInput.fromJson(json);
}

/// @nodoc
mixin _$JourneyContentInput {
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  List<String> get prompts => throw _privateConstructorUsedError;
  bool get isPublished => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  int? get estimatedDuration => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContentInput value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContentInput value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContentInput value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this JourneyContentInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JourneyContentInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JourneyContentInputCopyWith<JourneyContentInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JourneyContentInputCopyWith<$Res> {
  factory $JourneyContentInputCopyWith(
          JourneyContentInput value, $Res Function(JourneyContentInput) then) =
      _$JourneyContentInputCopyWithImpl<$Res, JourneyContentInput>;
  @useResult
  $Res call(
      {String title,
      String description,
      String category,
      String difficulty,
      List<String> prompts,
      bool isPublished,
      String? imageUrl,
      int? estimatedDuration,
      List<String>? tags,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$JourneyContentInputCopyWithImpl<$Res, $Val extends JourneyContentInput>
    implements $JourneyContentInputCopyWith<$Res> {
  _$JourneyContentInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JourneyContentInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? difficulty = null,
    Object? prompts = null,
    Object? isPublished = null,
    Object? imageUrl = freezed,
    Object? estimatedDuration = freezed,
    Object? tags = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value.prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JourneyContentInputImplCopyWith<$Res>
    implements $JourneyContentInputCopyWith<$Res> {
  factory _$$JourneyContentInputImplCopyWith(_$JourneyContentInputImpl value,
          $Res Function(_$JourneyContentInputImpl) then) =
      __$$JourneyContentInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String title,
      String description,
      String category,
      String difficulty,
      List<String> prompts,
      bool isPublished,
      String? imageUrl,
      int? estimatedDuration,
      List<String>? tags,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$JourneyContentInputImplCopyWithImpl<$Res>
    extends _$JourneyContentInputCopyWithImpl<$Res, _$JourneyContentInputImpl>
    implements _$$JourneyContentInputImplCopyWith<$Res> {
  __$$JourneyContentInputImplCopyWithImpl(_$JourneyContentInputImpl _value,
      $Res Function(_$JourneyContentInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of JourneyContentInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = null,
    Object? category = null,
    Object? difficulty = null,
    Object? prompts = null,
    Object? isPublished = null,
    Object? imageUrl = freezed,
    Object? estimatedDuration = freezed,
    Object? tags = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$JourneyContentInputImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      prompts: null == prompts
          ? _value._prompts
          : prompts // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isPublished: null == isPublished
          ? _value.isPublished
          : isPublished // ignore: cast_nullable_to_non_nullable
              as bool,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JourneyContentInputImpl implements _JourneyContentInput {
  const _$JourneyContentInputImpl(
      {required this.title,
      required this.description,
      required this.category,
      required this.difficulty,
      required final List<String> prompts,
      this.isPublished = false,
      this.imageUrl,
      this.estimatedDuration,
      final List<String>? tags,
      final Map<String, dynamic>? metadata})
      : _prompts = prompts,
        _tags = tags,
        _metadata = metadata;

  factory _$JourneyContentInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$JourneyContentInputImplFromJson(json);

  @override
  final String title;
  @override
  final String description;
  @override
  final String category;
  @override
  final String difficulty;
  final List<String> _prompts;
  @override
  List<String> get prompts {
    if (_prompts is EqualUnmodifiableListView) return _prompts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prompts);
  }

  @override
  @JsonKey()
  final bool isPublished;
  @override
  final String? imageUrl;
  @override
  final int? estimatedDuration;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'JourneyContentInput(title: $title, description: $description, category: $category, difficulty: $difficulty, prompts: $prompts, isPublished: $isPublished, imageUrl: $imageUrl, estimatedDuration: $estimatedDuration, tags: $tags, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JourneyContentInputImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            const DeepCollectionEquality().equals(other._prompts, _prompts) &&
            (identical(other.isPublished, isPublished) ||
                other.isPublished == isPublished) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      description,
      category,
      difficulty,
      const DeepCollectionEquality().hash(_prompts),
      isPublished,
      imageUrl,
      estimatedDuration,
      const DeepCollectionEquality().hash(_tags),
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of JourneyContentInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JourneyContentInputImplCopyWith<_$JourneyContentInputImpl> get copyWith =>
      __$$JourneyContentInputImplCopyWithImpl<_$JourneyContentInputImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContentInput value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContentInput value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContentInput value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$JourneyContentInputImplToJson(
      this,
    );
  }
}

abstract class _JourneyContentInput implements JourneyContentInput {
  const factory _JourneyContentInput(
      {required final String title,
      required final String description,
      required final String category,
      required final String difficulty,
      required final List<String> prompts,
      final bool isPublished,
      final String? imageUrl,
      final int? estimatedDuration,
      final List<String>? tags,
      final Map<String, dynamic>? metadata}) = _$JourneyContentInputImpl;

  factory _JourneyContentInput.fromJson(Map<String, dynamic> json) =
      _$JourneyContentInputImpl.fromJson;

  @override
  String get title;
  @override
  String get description;
  @override
  String get category;
  @override
  String get difficulty;
  @override
  List<String> get prompts;
  @override
  bool get isPublished;
  @override
  String? get imageUrl;
  @override
  int? get estimatedDuration;
  @override
  List<String>? get tags;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of JourneyContentInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JourneyContentInputImplCopyWith<_$JourneyContentInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

JourneyContentStats _$JourneyContentStatsFromJson(Map<String, dynamic> json) {
  return _JourneyContentStats.fromJson(json);
}

/// @nodoc
mixin _$JourneyContentStats {
  int get totalJourneys => throw _privateConstructorUsedError;
  int get publishedJourneys => throw _privateConstructorUsedError;
  int get draftJourneys => throw _privateConstructorUsedError;
  Map<String, int> get categoryCounts => throw _privateConstructorUsedError;
  Map<String, int> get difficultyCounts => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContentStats value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContentStats value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContentStats value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this JourneyContentStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JourneyContentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JourneyContentStatsCopyWith<JourneyContentStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JourneyContentStatsCopyWith<$Res> {
  factory $JourneyContentStatsCopyWith(
          JourneyContentStats value, $Res Function(JourneyContentStats) then) =
      _$JourneyContentStatsCopyWithImpl<$Res, JourneyContentStats>;
  @useResult
  $Res call(
      {int totalJourneys,
      int publishedJourneys,
      int draftJourneys,
      Map<String, int> categoryCounts,
      Map<String, int> difficultyCounts,
      DateTime lastUpdated});
}

/// @nodoc
class _$JourneyContentStatsCopyWithImpl<$Res, $Val extends JourneyContentStats>
    implements $JourneyContentStatsCopyWith<$Res> {
  _$JourneyContentStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JourneyContentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalJourneys = null,
    Object? publishedJourneys = null,
    Object? draftJourneys = null,
    Object? categoryCounts = null,
    Object? difficultyCounts = null,
    Object? lastUpdated = null,
  }) {
    return _then(_value.copyWith(
      totalJourneys: null == totalJourneys
          ? _value.totalJourneys
          : totalJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      publishedJourneys: null == publishedJourneys
          ? _value.publishedJourneys
          : publishedJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      draftJourneys: null == draftJourneys
          ? _value.draftJourneys
          : draftJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      categoryCounts: null == categoryCounts
          ? _value.categoryCounts
          : categoryCounts // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      difficultyCounts: null == difficultyCounts
          ? _value.difficultyCounts
          : difficultyCounts // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JourneyContentStatsImplCopyWith<$Res>
    implements $JourneyContentStatsCopyWith<$Res> {
  factory _$$JourneyContentStatsImplCopyWith(_$JourneyContentStatsImpl value,
          $Res Function(_$JourneyContentStatsImpl) then) =
      __$$JourneyContentStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalJourneys,
      int publishedJourneys,
      int draftJourneys,
      Map<String, int> categoryCounts,
      Map<String, int> difficultyCounts,
      DateTime lastUpdated});
}

/// @nodoc
class __$$JourneyContentStatsImplCopyWithImpl<$Res>
    extends _$JourneyContentStatsCopyWithImpl<$Res, _$JourneyContentStatsImpl>
    implements _$$JourneyContentStatsImplCopyWith<$Res> {
  __$$JourneyContentStatsImplCopyWithImpl(_$JourneyContentStatsImpl _value,
      $Res Function(_$JourneyContentStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of JourneyContentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalJourneys = null,
    Object? publishedJourneys = null,
    Object? draftJourneys = null,
    Object? categoryCounts = null,
    Object? difficultyCounts = null,
    Object? lastUpdated = null,
  }) {
    return _then(_$JourneyContentStatsImpl(
      totalJourneys: null == totalJourneys
          ? _value.totalJourneys
          : totalJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      publishedJourneys: null == publishedJourneys
          ? _value.publishedJourneys
          : publishedJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      draftJourneys: null == draftJourneys
          ? _value.draftJourneys
          : draftJourneys // ignore: cast_nullable_to_non_nullable
              as int,
      categoryCounts: null == categoryCounts
          ? _value._categoryCounts
          : categoryCounts // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      difficultyCounts: null == difficultyCounts
          ? _value._difficultyCounts
          : difficultyCounts // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JourneyContentStatsImpl implements _JourneyContentStats {
  const _$JourneyContentStatsImpl(
      {required this.totalJourneys,
      required this.publishedJourneys,
      required this.draftJourneys,
      required final Map<String, int> categoryCounts,
      required final Map<String, int> difficultyCounts,
      required this.lastUpdated})
      : _categoryCounts = categoryCounts,
        _difficultyCounts = difficultyCounts;

  factory _$JourneyContentStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$JourneyContentStatsImplFromJson(json);

  @override
  final int totalJourneys;
  @override
  final int publishedJourneys;
  @override
  final int draftJourneys;
  final Map<String, int> _categoryCounts;
  @override
  Map<String, int> get categoryCounts {
    if (_categoryCounts is EqualUnmodifiableMapView) return _categoryCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_categoryCounts);
  }

  final Map<String, int> _difficultyCounts;
  @override
  Map<String, int> get difficultyCounts {
    if (_difficultyCounts is EqualUnmodifiableMapView) return _difficultyCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_difficultyCounts);
  }

  @override
  final DateTime lastUpdated;

  @override
  String toString() {
    return 'JourneyContentStats(totalJourneys: $totalJourneys, publishedJourneys: $publishedJourneys, draftJourneys: $draftJourneys, categoryCounts: $categoryCounts, difficultyCounts: $difficultyCounts, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JourneyContentStatsImpl &&
            (identical(other.totalJourneys, totalJourneys) ||
                other.totalJourneys == totalJourneys) &&
            (identical(other.publishedJourneys, publishedJourneys) ||
                other.publishedJourneys == publishedJourneys) &&
            (identical(other.draftJourneys, draftJourneys) ||
                other.draftJourneys == draftJourneys) &&
            const DeepCollectionEquality()
                .equals(other._categoryCounts, _categoryCounts) &&
            const DeepCollectionEquality()
                .equals(other._difficultyCounts, _difficultyCounts) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalJourneys,
      publishedJourneys,
      draftJourneys,
      const DeepCollectionEquality().hash(_categoryCounts),
      const DeepCollectionEquality().hash(_difficultyCounts),
      lastUpdated);

  /// Create a copy of JourneyContentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JourneyContentStatsImplCopyWith<_$JourneyContentStatsImpl> get copyWith =>
      __$$JourneyContentStatsImplCopyWithImpl<_$JourneyContentStatsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_JourneyContentStats value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_JourneyContentStats value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_JourneyContentStats value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$JourneyContentStatsImplToJson(
      this,
    );
  }
}

abstract class _JourneyContentStats implements JourneyContentStats {
  const factory _JourneyContentStats(
      {required final int totalJourneys,
      required final int publishedJourneys,
      required final int draftJourneys,
      required final Map<String, int> categoryCounts,
      required final Map<String, int> difficultyCounts,
      required final DateTime lastUpdated}) = _$JourneyContentStatsImpl;

  factory _JourneyContentStats.fromJson(Map<String, dynamic> json) =
      _$JourneyContentStatsImpl.fromJson;

  @override
  int get totalJourneys;
  @override
  int get publishedJourneys;
  @override
  int get draftJourneys;
  @override
  Map<String, int> get categoryCounts;
  @override
  Map<String, int> get difficultyCounts;
  @override
  DateTime get lastUpdated;

  /// Create a copy of JourneyContentStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JourneyContentStatsImplCopyWith<_$JourneyContentStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
