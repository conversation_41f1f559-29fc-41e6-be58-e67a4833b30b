import 'package:freezed_annotation/freezed_annotation.dart';

part 'result.freezed.dart';

/// A generic result type that represents either success or failure
@freezed
class Result<T, E> with _$Result<T, E> {
  const Result._();

  const factory Result.success(T data) = Success<T, E>;
  const factory Result.failure(E error) = Failure<T, E>;

  /// Check if the result is a success
  bool get isSuccess => when(
        success: (_) => true,
        failure: (_) => false,
      );

  /// Check if the result is a failure
  bool get isFailure => !isSuccess;

  /// Get the data if success, null otherwise
  T? get data => when(
        success: (data) => data,
        failure: (_) => null,
      );

  /// Get the error if failure, null otherwise
  E? get error => when(
        success: (_) => null,
        failure: (error) => error,
      );

  /// Transform the success value
  Result<U, E> mapData<U>(U Function(T) mapper) {
    return when(
      success: (data) => Result.success(mapper(data)),
      failure: (error) => Result.failure(error),
    );
  }

  /// Transform the error value
  Result<T, F> mapError<F>(F Function(E) mapper) {
    return when(
      success: (data) => Result.success(data),
      failure: (error) => Result.failure(mapper(error)),
    );
  }

  /// Chain operations that return Results
  Result<U, E> flatMap<U>(Result<U, E> Function(T) mapper) {
    return when(
      success: (data) => mapper(data),
      failure: (error) => Result.failure(error),
    );
  }

  /// Get the value or throw the error
  T unwrap() {
    return when(
      success: (data) => data,
      failure: (error) => throw error!,
    );
  }

  /// Get the value or return a default
  T unwrapOr(T defaultValue) {
    return when(
      success: (data) => data,
      failure: (_) => defaultValue,
    );
  }

  /// Get the value or compute a default from the error
  T unwrapOrElse(T Function(E) defaultValue) {
    return when(
      success: (data) => data,
      failure: (error) => defaultValue(error),
    );
  }
}

/// Application-specific error types
@freezed
class AppError with _$AppError {
  const AppError._();

  const factory AppError.network(String message) = NetworkError;
  const factory AppError.authentication(String message) = AuthenticationError;
  const factory AppError.authorization(String message) = AuthorizationError;
  const factory AppError.database(String message) = DatabaseError;
  const factory AppError.validation(
      String message, Map<String, String>? fieldErrors) = ValidationError;
  const factory AppError.notFound(String message) = NotFoundError;
  const factory AppError.conflict(String message) = ConflictError;
  const factory AppError.unknown(String message) = UnknownError;

  /// Get a user-friendly error message
  String get message => when(
        network: (msg) => 'Network error: $msg',
        authentication: (msg) => 'Authentication error: $msg',
        authorization: (msg) => 'Authorization error: $msg',
        database: (msg) => 'Database error: $msg',
        validation: (msg, _) => 'Validation error: $msg',
        notFound: (msg) => 'Not found: $msg',
        conflict: (msg) => 'Conflict: $msg',
        unknown: (msg) => 'Unknown error: $msg',
      );

  /// Get error code for logging/debugging
  String get code => when(
        network: (_) => 'NETWORK_ERROR',
        authentication: (_) => 'AUTH_ERROR',
        authorization: (_) => 'AUTHZ_ERROR',
        database: (_) => 'DB_ERROR',
        validation: (_, __) => 'VALIDATION_ERROR',
        notFound: (_) => 'NOT_FOUND',
        conflict: (_) => 'CONFLICT',
        unknown: (_) => 'UNKNOWN_ERROR',
      );

  /// Check if error is retryable
  bool get isRetryable => when(
        network: (_) => true,
        authentication: (_) => false,
        authorization: (_) => false,
        database: (_) => true,
        validation: (_, __) => false,
        notFound: (_) => false,
        conflict: (_) => false,
        unknown: (_) => false,
      );

  /// Get HTTP status code equivalent
  int get statusCode => when(
        network: (_) => 503,
        authentication: (_) => 401,
        authorization: (_) => 403,
        database: (_) => 500,
        validation: (_, __) => 400,
        notFound: (_) => 404,
        conflict: (_) => 409,
        unknown: (_) => 500,
      );
}
