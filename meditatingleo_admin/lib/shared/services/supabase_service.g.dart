// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'supabase_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$supabaseHash() => r'6abeb04f6d303eb0dd323e0401492c39546c0428';

/// Supabase client provider for MeditatingLeo Admin Panel
///
/// This provider configures and provides the Supabase client instance
/// for the admin application with proper error handling and configuration.
///
/// Features:
/// - Singleton Supabase client
/// - Environment-based configuration
/// - Admin-specific settings
/// - Error handling
///
/// Copied from [supabase].
@ProviderFor(supabase)
final supabaseProvider = AutoDisposeProvider<SupabaseClient>.internal(
  supabase,
  name: r'supabaseProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$supabaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupabaseRef = AutoDisposeProviderRef<SupabaseClient>;
String _$supabaseServiceHash() => r'60f829fa8c719d57c44209a77761d5ff42fd58a4';

/// Supabase service for admin-specific operations
///
/// This service provides high-level methods for interacting with Supabase
/// specifically for admin operations like content management, user administration,
/// and system monitoring.
///
/// Features:
/// - Admin authentication
/// - Content management operations
/// - User administration
/// - System monitoring
/// - Error handling and logging
///
/// Copied from [supabaseService].
@ProviderFor(supabaseService)
final supabaseServiceProvider = AutoDisposeProvider<SupabaseService>.internal(
  supabaseService,
  name: r'supabaseServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$supabaseServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SupabaseServiceRef = AutoDisposeProviderRef<SupabaseService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
