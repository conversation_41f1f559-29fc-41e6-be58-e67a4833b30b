import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:typed_data';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

part 'supabase_service.g.dart';

/// Supabase client provider for MeditatingLeo Admin Panel
///
/// This provider configures and provides the Supabase client instance
/// for the admin application with proper error handling and configuration.
///
/// Features:
/// - Singleton Supabase client
/// - Environment-based configuration
/// - Admin-specific settings
/// - Error handling
@riverpod
SupabaseClient supabase(Ref ref) {
  return Supabase.instance.client;
}

/// Supabase service for admin-specific operations
///
/// This service provides high-level methods for interacting with Supabase
/// specifically for admin operations like content management, user administration,
/// and system monitoring.
///
/// Features:
/// - Admin authentication
/// - Content management operations
/// - User administration
/// - System monitoring
/// - Error handling and logging
@riverpod
SupabaseService supabaseService(Ref ref) {
  final client = ref.watch(supabaseProvider);
  return SupabaseService(client);
}

/// Service class for Supabase operations
class SupabaseService {
  final SupabaseClient _client;

  SupabaseService(this._client);

  // Authentication Methods

  /// Sign in with email and password
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      return await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  /// Get current user
  User? get currentUser => _client.auth.currentUser;

  /// Get current session
  Session? get currentSession => _client.auth.currentSession;

  /// Listen to auth state changes
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  /// Refresh current session
  Future<AuthResponse> refreshSession() async {
    try {
      return await _client.auth.refreshSession();
    } catch (e) {
      throw _handleAuthError(e);
    }
  }

  // Database Methods

  /// Get a reference to a table
  SupabaseQueryBuilder from(String table) {
    return _client.from(table);
  }

  /// Execute a raw SQL query
  Future<PostgrestResponse> rpc(
    String functionName, {
    Map<String, dynamic>? params,
  }) async {
    try {
      return await _client.rpc(functionName, params: params);
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  // Content Management Methods

  /// Get all journeys with pagination
  Future<List<Map<String, dynamic>>> getJourneys({
    int? limit,
    int? offset,
    String? search,
  }) async {
    try {
      dynamic query = _client.from('journeys').select();

      if (search != null && search.isNotEmpty) {
        query = query.ilike('title', '%$search%');
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query.order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  /// Create a new journey
  Future<Map<String, dynamic>> createJourney(
      Map<String, dynamic> journey) async {
    try {
      final response =
          await _client.from('journeys').insert(journey).select().single();
      return response;
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  /// Update an existing journey
  Future<Map<String, dynamic>> updateJourney(
    String id,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _client
          .from('journeys')
          .update(updates)
          .eq('id', id)
          .select()
          .single();
      return response;
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  /// Delete a journey
  Future<void> deleteJourney(String id) async {
    try {
      await _client.from('journeys').delete().eq('id', id);
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  // User Management Methods

  /// Get all users with pagination
  Future<List<Map<String, dynamic>>> getUsers({
    int? limit,
    int? offset,
    String? search,
  }) async {
    try {
      dynamic query = _client.from('profiles').select();

      if (search != null && search.isNotEmpty) {
        query = query.or('email.ilike.%$search%,full_name.ilike.%$search%');
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      }

      final response = await query.order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateUserProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _client
          .from('profiles')
          .update(updates)
          .eq('id', userId)
          .select()
          .single();
      return response;
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  // Analytics Methods

  /// Get user analytics
  Future<Map<String, dynamic>> getUserAnalytics() async {
    try {
      final response = await _client.rpc('get_user_analytics');
      return response.data;
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  /// Get content analytics
  Future<Map<String, dynamic>> getContentAnalytics() async {
    try {
      final response = await _client.rpc('get_content_analytics');
      return response.data;
    } catch (e) {
      throw _handleDatabaseError(e);
    }
  }

  // Storage Methods

  /// Upload file to storage
  Future<String> uploadFile({
    required String bucket,
    required String path,
    required Uint8List bytes,
    String? contentType,
  }) async {
    try {
      await _client.storage.from(bucket).uploadBinary(
            path,
            bytes,
            fileOptions: FileOptions(
              contentType: contentType,
            ),
          );

      return _client.storage.from(bucket).getPublicUrl(path);
    } catch (e) {
      throw _handleStorageError(e);
    }
  }

  /// Delete file from storage
  Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await _client.storage.from(bucket).remove([path]);
    } catch (e) {
      throw _handleStorageError(e);
    }
  }

  // Real-time Methods

  /// Subscribe to table changes
  RealtimeChannel subscribeToTable(
    String table, {
    PostgresChangeFilter? filter,
    void Function(PostgresChangePayload)? onInsert,
    void Function(PostgresChangePayload)? onUpdate,
    void Function(PostgresChangePayload)? onDelete,
  }) {
    final channel = _client.channel('admin_$table');

    if (onInsert != null) {
      channel.onPostgresChanges(
        event: PostgresChangeEvent.insert,
        schema: 'public',
        table: table,
        filter: filter,
        callback: onInsert,
      );
    }

    if (onUpdate != null) {
      channel.onPostgresChanges(
        event: PostgresChangeEvent.update,
        schema: 'public',
        table: table,
        filter: filter,
        callback: onUpdate,
      );
    }

    if (onDelete != null) {
      channel.onPostgresChanges(
        event: PostgresChangeEvent.delete,
        schema: 'public',
        table: table,
        filter: filter,
        callback: onDelete,
      );
    }

    channel.subscribe();
    return channel;
  }

  // Error Handling

  Exception _handleAuthError(dynamic error) {
    if (error is AuthException) {
      return AdminAuthException(error.message);
    }
    return AdminException('Authentication error: ${error.toString()}');
  }

  Exception _handleDatabaseError(dynamic error) {
    if (error is PostgrestException) {
      return AdminDatabaseException(error.message);
    }
    return AdminException('Database error: ${error.toString()}');
  }

  Exception _handleStorageError(dynamic error) {
    if (error is StorageException) {
      return AdminStorageException(error.message);
    }
    return AdminException('Storage error: ${error.toString()}');
  }
}

// Custom Exception Classes

/// Base exception for admin operations
class AdminException implements Exception {
  final String message;
  AdminException(this.message);

  @override
  String toString() => 'AdminException: $message';
}

/// Authentication-specific exception
class AdminAuthException extends AdminException {
  AdminAuthException(super.message);

  @override
  String toString() => 'AdminAuthException: $message';
}

/// Database-specific exception
class AdminDatabaseException extends AdminException {
  AdminDatabaseException(super.message);

  @override
  String toString() => 'AdminDatabaseException: $message';
}

/// Storage-specific exception
class AdminStorageException extends AdminException {
  AdminStorageException(super.message);

  @override
  String toString() => 'AdminStorageException: $message';
}
