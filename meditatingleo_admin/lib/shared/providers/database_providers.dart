import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/core/database/admin_database.dart';
import 'package:meditatingleo_admin/core/services/supabase_service.dart';

part 'database_providers.g.dart';

/// Provides the Supabase client instance
@riverpod
SupabaseClient supabaseClient(SupabaseClientRef ref) {
  return Supabase.instance.client;
}

/// Provides the admin database instance
@riverpod
AdminDatabase adminDatabase(AdminDatabaseRef ref) {
  return AdminDatabase.file();
}

/// Provides the Supabase service with admin capabilities
@riverpod
SupabaseService supabaseService(SupabaseServiceRef ref) {
  final client = ref.watch(supabaseClientProvider);
  return SupabaseService(client);
}

/// Provides the current authentication state
@riverpod
Stream<AuthState> authStateStream(AuthStateStreamRef ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.authStateStream;
}

/// Provides the current authenticated user
@riverpod
User? currentUser(CurrentUserRef ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.getCurrentUser();
}

/// Provides admin permission check
@riverpod
bool hasAdminPermissions(HasAdminPermissionsRef ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.hasAdminPermissions();
}

/// Provides user role
@riverpod
String? userRole(UserRoleRef ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.getUserRole();
}

/// Provides user permissions
@riverpod
List<String> userPermissions(UserPermissionsRef ref) {
  final service = ref.watch(supabaseServiceProvider);
  return service.getUserPermissions();
}
