import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:meditatingleo_admin/core/repositories/content_repository.dart';
import 'package:meditatingleo_admin/shared/providers/database_providers.dart';

part 'repository_providers.g.dart';

/// Provides the content repository for managing journey content
@riverpod
ContentRepository contentRepository(ContentRepositoryRef ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final database = ref.watch(adminDatabaseProvider);
  return ContentRepository(supabaseService, database);
}
