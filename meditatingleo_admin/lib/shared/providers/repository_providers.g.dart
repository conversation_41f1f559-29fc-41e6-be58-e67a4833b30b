// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'repository_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$contentRepositoryHash() => r'230c469661947a431616cb4b7e7ec6e68efd3dee';

/// Provides the content repository for managing journey content
///
/// Copied from [contentRepository].
@ProviderFor(contentRepository)
final contentRepositoryProvider =
    AutoDisposeProvider<ContentRepository>.internal(
  contentRepository,
  name: r'contentRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$contentRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ContentRepositoryRef = AutoDisposeProviderRef<ContentRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
