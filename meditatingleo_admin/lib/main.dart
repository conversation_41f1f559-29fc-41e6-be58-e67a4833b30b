import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'app/app.dart';
import 'core/constants/app_constants.dart';

/// Main entry point for MeditatingLeo Admin Panel
///
/// This function initializes the Flutter application with:
/// - Supabase configuration
/// - Desktop window management
/// - Riverpod state management
/// - Error handling
///
/// Following Flutter 2025+ standards with proper initialization sequence.
void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase
    await _initializeSupabase();

    // Run the application
    runApp(
      const ProviderScope(
        child: AdminApp(),
      ),
    );
  } catch (error, stackTrace) {
    // Handle initialization errors
    _handleInitializationError(error, stackTrace);
  }
}

/// Initialize Supabase with admin configuration
Future<void> _initializeSupabase() async {
  await Supabase.initialize(
    url: AppConstants.supabaseUrl,
    anonKey: AppConstants.supabaseAnonKey,
    debug: AppConstants.isDevelopment,
  );
}

/// Handle initialization errors gracefully
void _handleInitializationError(Object error, StackTrace stackTrace) {
  // Log the error
  debugPrint('Initialization Error: $error');
  debugPrint('Stack Trace: $stackTrace');

  // Run a minimal error app
  runApp(
    MaterialApp(
      title: AppConstants.adminAppName,
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Initialization Failed',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to initialize the admin panel.\n'
                  'Please check your configuration and try again.\n\n'
                  'Error: ${error.toString()}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
