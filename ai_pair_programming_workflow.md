# Complete Flutter AI Pair Programming Guide for Beginners
*A Step-by-Step Workflow for Building Flutter Mobile Apps with AI Assistance*

## Table of Contents
1. [Getting Started: Define Your Vision](#step-1-define-your-vision-and-project-requirements)
2. [Modern Flutter Standards and Best Practices](#modern-flutter-standards-and-best-practices)
3. [Flutter Development Standards](#flutter-development-standards)
4. [Set Up Your Memory Bank System](#step-2-create-your-project-brief-and-initialize-the-memory-bank)
5. [Plan Your Project](#step-3-plan-and-break-down-tasks-using-ai-task-management)
6. [Build Features with Tests](#step-4-implement-features-using-test-driven-development-tdd)
7. [Review and Debug Code](#step-5-critical-code-review-and-debugging)
8. [Maintain and Improve](#step-6-maintain-context-and-continuously-improve)

---

## Before You Begin: What You Need

### Required Tools
1. **A Computer** with internet access (Windows, Mac, or Linux)
2. **An AI Assistant** (like <PERSON><PERSON><PERSON><PERSON>, <PERSON>, or similar)
3. **Flutter Development Environment**:
   - Flutter SDK (we'll help you install this)
   - Android Studio or VS Code with Flutter extensions
   - Android Emulator or physical device for testing
4. **A Web Browser** for research and testing

### What This Guide Assumes
- You have **zero programming experience** - that's perfectly fine!
- You have **zero Flutter experience** - we'll guide you through everything
- You can copy and paste text
- You can create folders and files on your computer
- You want to build a **mobile app** using Flutter

### Flutter Technology Stack We'll Use
- **Frontend Framework**: Flutter with Dart programming language
- **State Management**: Riverpod (for managing app data and state)
- **Local Database**: Drift (for storing data offline on the device)
- **Backend Database**: Supabase or Firebase (we'll help you choose)
- **Testing**: Flutter's built-in testing framework
- **Architecture**: Feature-first folder structure with clean separation

### How to Use This Guide
- Follow each step in order
- Copy and paste the exact prompts provided
- Don't skip steps - each builds on the previous one
- When you see `[PLACEHOLDER]`, replace it with your specific information
- All code examples are Flutter/Dart specific

---

## Modern Flutter Standards and Best Practices

### What This Section Does
Ensures you learn and implement the most current Flutter and Dart practices (2025+) from the beginning. This prevents you from accidentally using deprecated methods that will need to be refactored later.

### Current Flutter Standards (2025+)

#### 1. Modern Color Management
**✅ Use:** `Color.withValues()` for color manipulation
```dart
// ✅ CORRECT - Modern approach (2025+)
final transparentColor = Colors.blue.withValues(alpha: 0.5);
final lighterColor = Colors.red.withValues(red: 0.8);

// ❌ AVOID - Deprecated approach
final transparentColor = Colors.blue.withOpacity(0.5); // Deprecated
```

#### 2. Modern Riverpod State Management
**✅ Use:** Riverpod code generation with `@riverpod` annotation
```dart
// ✅ CORRECT - Modern Riverpod with code generation (2025+)
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'task_provider.g.dart';

@riverpod
class TaskNotifier extends _$TaskNotifier {
  @override
  List<Task> build() => [];

  void addTask(Task task) {
    state = [...state, task];
  }
}

// ❌ AVOID - Deprecated StateNotifier approach
class TaskNotifier extends StateNotifier<List<Task>> {
  TaskNotifier() : super([]);
  // Old pattern - avoid this
}
```

#### 3. Modern Material Design 3 Components
**✅ Use:** Material Design 3 (Material You) components
```dart
// ✅ CORRECT - Material Design 3 components
FilledButton(onPressed: () {}, child: Text('Submit')),
Card.filled(child: ListTile(...)),
NavigationBar(destinations: [...]),

// ❌ AVOID - Older Material Design components when M3 alternatives exist
RaisedButton(...), // Use FilledButton instead
FlatButton(...),   // Use TextButton instead
```

#### 4. Modern Widget Patterns
**✅ Use:** Current widget constructors and patterns
```dart
// ✅ CORRECT - Modern widget patterns
const SizedBox.shrink(), // For empty widgets
const Divider(height: 1),
const Padding(padding: EdgeInsets.all(16.0)),

// ❌ AVOID - Deprecated or outdated patterns
Container(), // When SizedBox.shrink() is more appropriate
```

### AI Prompt Enhancement for Modern Standards

**CRITICAL:** Every AI prompt in this guide includes this instruction to ensure modern practices:

```
IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:

- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach

Before providing any code, verify that all APIs and patterns are current and not deprecated. If you're uncertain about the most modern approach for any Flutter concept, please ask for clarification.
```

### Integration with MCP Context

When working with AI assistants that support Model Context Protocol (MCP), use these enhanced instructions:

```
Please leverage your context capabilities to:
- Verify that all suggested Flutter code uses current, non-deprecated APIs
- Check that Riverpod patterns follow the latest code generation recommendations
- Ensure all Flutter widgets and methods are from the current stable release
- Cross-reference against the latest Flutter and Dart documentation
- Flag any potentially outdated patterns or methods

If you have access to real-time documentation or context tools, please use them to validate that all suggestions follow 2025+ Flutter best practices.
```

### Code Review Enhancement for Modern Standards

All code review prompts in this guide will specifically check for:

#### Modern Flutter Checklist
- [ ] **Color Management**: Uses `Color.withValues()` instead of deprecated `Color.withOpacity()`
- [ ] **State Management**: Uses Riverpod code generation (`@riverpod`) instead of `StateNotifier`
- [ ] **Material Design**: Uses Material Design 3 components where available
- [ ] **Widget Patterns**: Uses modern widget constructors and patterns
- [ ] **Dependencies**: All packages are current versions without deprecated APIs
- [ ] **Dart Features**: Uses current Dart language features and syntax

#### Deprecation Detection
- [ ] **No Deprecated Methods**: Code doesn't use any deprecated Flutter or Dart methods
- [ ] **No Deprecated Packages**: Dependencies don't include deprecated packages
- [ ] **No Deprecated Patterns**: Code follows current architectural patterns
- [ ] **Future-Proof**: Code uses stable APIs that won't be deprecated soon

### Why This Matters for Beginners

**Learning Modern Practices First:**
- Avoids the need to unlearn deprecated patterns later
- Ensures your code will work with future Flutter versions
- Follows current community best practices
- Makes your code more maintainable and performant

**Avoiding Technical Debt:**
- Prevents accumulation of deprecated code
- Reduces future refactoring work
- Ensures compatibility with latest Flutter features
- Maintains code quality from the start

---

## Flutter Development Standards

### What This Section Does
Establishes the specific Flutter development standards and architectural guidelines we'll follow throughout the project. These standards ensure your app is well-organized, maintainable, and follows Flutter best practices.

### Flutter Architecture Requirements

#### 1. Feature-First Folder Structure
We'll organize code by features (what the app does) rather than by file types:

```
lib/
├── core/                    # Shared utilities and constants
│   ├── constants/
│   ├── utils/
│   └── theme/
├── features/               # All app features
│   ├── authentication/    # Login/signup feature
│   │   ├── data/         # Data layer (repositories, models)
│   │   ├── domain/       # Business logic
│   │   └── presentation/ # UI widgets and screens
│   ├── tasks/            # Task management feature
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   └── profile/          # User profile feature
├── shared/               # Shared widgets and components
│   ├── widgets/
│   └── providers/
└── main.dart            # App entry point
```

#### 2. Code Organization Standards
- **One Widget Per File**: Each Flutter widget must be in its own separate file
- **Maximum File Length**: 200 lines of code per file
- **Maximum Function Length**: 50 lines of code per function/method
- **One Functionality Per Method**: Each method should do one specific thing

#### 3. Flutter Naming Conventions
- **Classes and Widgets**: PascalCase (e.g., `TaskListWidget`, `UserProfile`)
- **Variables and Functions**: camelCase (e.g., `taskList`, `getUserData()`)
- **Files**: snake_case (e.g., `task_list_widget.dart`, `user_profile.dart`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `MAX_TASK_LENGTH`)

#### 4. Code Documentation Requirements
Every code segment must include comments explaining:
- What the code does
- Why it's needed
- How it works (for complex logic)

**Example:**
```dart
/// Widget that displays a list of user tasks
/// Handles loading states and empty states automatically
class TaskListWidget extends ConsumerWidget {
  const TaskListWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the task list state from Riverpod provider
    final taskListAsync = ref.watch(taskListProvider);

    return taskListAsync.when(
      // Show loading spinner while tasks are being fetched
      loading: () => const CircularProgressIndicator(),
      // Handle error state with user-friendly message
      error: (error, stack) => Text('Error loading tasks: $error'),
      // Display the actual task list when data is available
      data: (tasks) => ListView.builder(
        itemCount: tasks.length,
        itemBuilder: (context, index) => TaskTile(task: tasks[index]),
      ),
    );
  }
}
```

#### 5. Widget Composition Over Inheritance
- Build complex UIs by combining simple widgets
- Avoid creating overly complex custom widgets
- Use Flutter's built-in widgets whenever possible

#### 6. Separation of Business Logic from UI
- UI widgets only handle display and user interaction
- Business logic goes in Riverpod providers
- Data operations go in repository classes

### Backend Decision Framework

#### When to Choose Supabase vs Firebase

**Use Supabase when:**
- You want a PostgreSQL database with SQL queries
- You need real-time subscriptions with complex filtering
- You prefer open-source solutions
- You want built-in authentication with Row Level Security
- Your app needs complex data relationships

**Use Firebase when:**
- You want the simplest setup and deployment
- You're building a simple app with basic data needs
- You want Google's ecosystem integration
- You need offline-first capabilities with Firestore
- You want extensive documentation and community support

**Decision Prompt for AI:**
```
Help me choose between Supabase and Firebase for my Flutter app. My app will:
- [Describe your app's main functionality]
- [Describe your data requirements]
- [Mention any specific features you need]

Based on this information, recommend which backend to use and explain why. Also provide the setup steps for the recommended option.
```

---

## Step 1: Define Your Vision and Project Requirements

### What This Step Does
Before building anything, you need to clearly define what you want to create. This step helps you organize your thoughts and communicate effectively with your AI assistant about your Flutter mobile app.

### What You'll Do
1. Write down your mobile app idea in simple terms
2. Identify who will use your app
3. List the main features you want
4. Get AI help to refine and organize these ideas for Flutter development

### Detailed Instructions

#### 1.1 Prepare Your Flutter App Information
Before talking to your AI assistant, write down:
- **What type of mobile app** you want to build (e.g., "a to-do list app", "a fitness tracking app", "a recipe sharing app")
- **Who will use it** (e.g., "busy professionals", "fitness enthusiasts", "cooking enthusiasts")
- **What problem it solves** (e.g., "helps people organize their daily tasks on their phone")
- **3-5 main features** you want (e.g., "add tasks", "mark tasks complete", "set push notifications")
- **Platform preference** (iOS, Android, or both)

#### 1.2 Use This Exact Flutter-Specific Prompt
Copy and paste this prompt into your AI assistant, replacing the bracketed sections with your information:

```
I want to build a Flutter mobile app that is a [type of application] that helps [target users] to [main purpose/problem solved].

My vision is: [describe your long-term vision in 2-3 sentences]

Key objectives:
- [Objective 1]
- [Objective 2]
- [Objective 3]

Target platforms: [iOS/Android/Both]

Please help me refine these goals and break them down into initial actionable requirements for a Flutter mobile app. Consider mobile-specific features like push notifications, offline functionality, and responsive design for different screen sizes.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**Example of a completed Flutter app prompt:**
```
I want to build a Flutter mobile app that is a task management application that helps busy professionals to organize their daily work and personal tasks efficiently on their mobile devices.

My vision is: Create a simple, intuitive mobile app that reduces stress by helping users track what they need to do and when, even when they're offline. The app should work seamlessly on both phones and tablets and help users feel more organized and productive.

Key objectives:
- Allow users to quickly add and organize tasks on their phone
- Help users prioritize what's most important with visual indicators
- Send push notifications for reminders and deadlines
- Work offline so users can access their tasks anywhere

Target platforms: Both iOS and Android

Please help me refine these goals and break them down into initial actionable requirements for a Flutter mobile app. Consider mobile-specific features like push notifications, offline functionality, and responsive design for different screen sizes.
```

#### 1.3 Review AI Response
Your AI assistant will respond with refined goals and Flutter-specific suggestions. Read through the response and:
- Ask questions if anything is unclear
- Request changes if something doesn't match your vision
- Ask for more details on any Flutter-specific suggestions that interest you
- Confirm the suggested mobile features make sense for your app

---

## Step 2: Create Your Project Brief and Initialize the Memory Bank

### What This Step Does
Creates a "memory system" for your AI assistant. Since AI assistants can't remember previous conversations, this system stores all important Flutter project information in files that you can reference in future conversations.

### What You'll Do
1. Create a Flutter project folder structure on your computer
2. Use AI to create detailed Flutter project documentation files
3. Review and customize these files for your Flutter app

### Detailed Instructions

#### 2.1 Create Your Flutter Project Folder Structure
1. **Open your file manager** (Windows Explorer on Windows, Finder on Mac)
2. **Navigate to your Documents folder** (or wherever you want to store your project)
3. **Create a new folder** called `my_flutter_app` (or whatever name you prefer)
4. **Open that folder**
5. **Create a new folder inside it** called `memory_bank`

Your folder structure should look like:
```
Documents/
  my_flutter_app/
    memory_bank/
    (Flutter project files will be added here later)
```

#### 2.2 Initialize Flutter Memory Bank with AI

**Copy and paste this exact prompt** into your AI assistant, replacing the bracketed sections:

```
I want to build a Flutter mobile app: [brief description of your app].

Let's initialize a Memory Bank system specifically for Flutter development to maintain persistent context throughout our development process. Please create the following specialized files in a memory_bank folder:

1. **project_brief.md** - Core goals, high-level requirements, and Flutter project scope
2. **product_context.md** - User experience elements, mobile workflows, and business logic
3. **active_context.md** - Current focus, recent changes, decisions, and emerging Flutter patterns
4. **system_patterns.md** - Flutter architectural decisions, widget patterns, and component relationships
5. **tech_context.md** - Flutter/Dart technology stack, dependencies, and tooling
6. **progress_tracking.md** - Working features, pending tasks, and known issues

For each file, create an initial structure with relevant sections and populate them based on our Flutter project discussion so far. Include Flutter-specific considerations like:
- Widget architecture and state management with Riverpod code generation (@riverpod)
- Mobile-specific features (push notifications, offline storage, responsive design)
- Flutter project structure and naming conventions
- Drift database schema planning
- Backend choice (Supabase vs Firebase) considerations

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

#### 2.3 Save the AI-Generated Files
Your AI assistant will create content for each file. For each file:

1. **Copy the content** the AI provides for each file
2. **Open your text editor** (like Notepad or VS Code)
3. **Paste the content** into a new document
4. **Save the file** in your `memory_bank` folder with the exact name provided (e.g., `project_brief.md`)
5. **Repeat for all 6 files**

**Important:** Make sure to save files with the `.md` extension as shown.

#### 2.4 Enhanced Memory Bank Creation

Now that you have the basic files, let's make them more comprehensive. Use these detailed prompts to enhance each file:

##### 2.4.1 Enhanced Flutter Project Brief
Copy and paste this prompt:

```
Create a comprehensive Flutter Project Brief document for our memory bank. This should serve as the foundational reference for all Flutter development work. Please structure it as follows:

**PROJECT OVERVIEW:**
- Flutter app name and brief description
- Core problem this mobile app solves
- Target audience and mobile use cases
- Primary goals and success metrics for mobile users

**FLUTTER-SPECIFIC SCOPE:**
- Target platforms (iOS, Android, or both)
- Minimum supported OS versions
- Device types supported (phones, tablets, etc.)
- Offline functionality requirements
- Push notification needs

**SCOPE AND BOUNDARIES:**
- What's included in this Flutter project
- What's explicitly out of scope
- Key mobile constraints and limitations
- Timeline and milestone expectations

**HIGH-LEVEL REQUIREMENTS:**
- Must-have features for MVP (mobile-first)
- Nice-to-have features (future iterations)
- Performance requirements (app startup time, responsiveness)
- Security and compliance needs for mobile apps

**MOBILE USER CONTEXT:**
- Primary mobile users and their needs
- Mobile usage patterns and scenarios
- Device capabilities required
- Network connectivity considerations

**TECHNICAL CONSTRAINTS:**
- Flutter version requirements
- State management approach (Riverpod)
- Database choice (Drift for local, Supabase/Firebase for backend)
- Third-party integrations needed

Save this as `memory_bank/project_brief.md` and ensure it's written in clear, non-technical language that any team member can understand. This document should remain relatively stable throughout the Flutter project lifecycle.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Copy the AI's response
2. Open your existing `project_brief.md` file
3. Replace the content with the new, enhanced version
4. Save the file

##### 2.4.2 Enhanced Flutter Product Context
Copy and paste this prompt:

```
Generate a detailed Flutter Product Context document that captures the mobile user experience and business logic aspects of our Flutter application. Structure this document as follows:

**MOBILE USER PERSONAS AND JOURNEYS:**
- Primary mobile user types and their characteristics
- Key mobile user workflows and interaction patterns
- Mobile-specific pain points this app addresses
- Success scenarios and user goals on mobile devices
- Typical usage contexts (on-the-go, at home, at work)

**FLUTTER FEATURE SPECIFICATIONS:**
- Detailed feature descriptions with mobile user stories
- Flutter widget requirements and UI expectations
- Mobile-specific data requirements for each feature
- Integration points with device features (camera, GPS, notifications)
- Offline functionality requirements for each feature

**MOBILE BUSINESS LOGIC AND RULES:**
- Core business rules that govern mobile app functionality
- Data validation requirements for mobile inputs
- Permission and access control logic for mobile users
- Error handling and edge cases specific to mobile
- Network connectivity handling (offline/online states)

**FLUTTER UX/UI GUIDELINES:**
- Material Design principles for Android
- Cupertino design principles for iOS
- Accessibility requirements (screen readers, large text)
- Responsive design for different screen sizes
- Brand guidelines and Flutter theming approach
- Animation and transition preferences

**MOBILE WORKFLOWS AND PROCESSES:**
- Step-by-step mobile user workflows
- Push notification workflows
- Background processing requirements
- Data synchronization between local and remote storage
- Integration with device features and third-party services

**FLUTTER-SPECIFIC CONSIDERATIONS:**
- Widget composition strategies
- State management patterns with Riverpod
- Navigation patterns and routing
- Performance considerations for mobile devices
- Platform-specific adaptations (iOS vs Android)

Save this as `memory_bank/product_context.md`. This document should be updated whenever new Flutter features are planned or mobile user requirements change.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Copy the AI's response
2. Open your existing `product_context.md` file
3. Replace the content with the new, enhanced version
4. Save the file

---

##### 2.4.3 Enhanced Active Context
Copy and paste this prompt:

```
Create and maintain an Active Context document that captures the current state of development. This is a living document that should be updated after each significant change or task completion. Structure it as:

**CURRENT FOCUS:**
- What we're currently working on
- Immediate priorities and blockers
- Recent decisions made and their rationale
- Active branches or development streams

**RECENT CHANGES:**
- Last 5-10 significant changes made
- Files modified in recent sessions
- New patterns or approaches introduced
- Bugs fixed or issues resolved

**EMERGING PATTERNS:**
- New coding patterns being established
- Architectural shifts in progress
- Refactoring opportunities identified
- Technical debt being addressed

**IMMEDIATE NEXT STEPS:**
- Tasks queued for next development session
- Pending code reviews or testing
- Known issues that need attention
- Dependencies waiting to be resolved

**CONTEXT FOR AI:**
- Specific instructions for maintaining consistency
- Areas where extra caution is needed
- Preferred approaches for current work
- Files that should not be modified

Save this as `memory_bank/active_context.md`. Update this document at the end of each development session and before starting new work. This helps maintain continuity across different AI interactions.
```

##### 2.4.4 Enhanced Flutter System Patterns
Copy and paste this prompt:

```
Generate a comprehensive Flutter System Patterns document that outlines the architectural decisions and design patterns used in this Flutter codebase. This ensures consistency in future Flutter development. Structure it as:

**FLUTTER ARCHITECTURAL OVERVIEW:**
- High-level Flutter app architecture and design philosophy
- Feature-first folder structure implementation
- Widget composition patterns and hierarchy
- State management architecture with Riverpod
- Data flow between UI, business logic, and data layers

**FLUTTER CODE ORGANIZATION:**
- Feature-first folder structure conventions
- Flutter/Dart naming conventions (PascalCase, camelCase, snake_case)
- Widget organization and file separation (one widget per file)
- Dependency management with pubspec.yaml
- Separation of concerns: UI, business logic, and data

**FLUTTER DESIGN PATTERNS:**
- Widget composition over inheritance patterns
- Riverpod provider patterns for state management
- Repository pattern for data access
- Drift database patterns for local storage
- Navigation and routing patterns
- Error handling patterns for mobile apps

**FLUTTER COMPONENT RELATIONSHIPS:**
- How widgets communicate with each other
- Data passing conventions between widgets
- Riverpod provider dependencies and relationships
- Event handling and user interaction patterns
- Integration with device features (camera, notifications, etc.)

**FLUTTER CODING STANDARDS:**
- Dart code formatting and style guidelines (dart format)
- Widget documentation standards
- Testing patterns and conventions for Flutter
- Performance optimization patterns for mobile
- Accessibility implementation patterns

**FLUTTER ANTI-PATTERNS TO AVOID:**
- Overly complex widget hierarchies
- State management anti-patterns
- Performance-killing patterns (unnecessary rebuilds)
- Platform-specific code in shared widgets
- Common Flutter mistakes to avoid

**MOBILE-SPECIFIC PATTERNS:**
- Responsive design patterns for different screen sizes
- Platform adaptation patterns (iOS vs Android)
- Background processing patterns
- Offline-first data patterns with Drift
- Push notification handling patterns

Save this as `memory_bank/system_patterns.md`. This document should be updated when new Flutter patterns are established or architectural decisions are made.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

##### 2.4.5 Enhanced Flutter Tech Context
Copy and paste this prompt:

```
Create a detailed Flutter Tech Context document that specifies all technical aspects of the Flutter project. This serves as the technical specification reference. Structure it as:

**FLUTTER TECHNOLOGY STACK:**
- Flutter SDK version and channel (stable/beta)
- Dart programming language version
- Target platforms (iOS, Android) and minimum versions
- Development tools and IDEs (VS Code, Android Studio)
- Device testing setup (emulators, physical devices)

**FLUTTER DEPENDENCIES AND PACKAGES:**
- Core Flutter dependencies with version numbers
- Riverpod for state management (version and configuration)
- Drift for local database (version and setup)
- Backend choice: Supabase or Firebase (with rationale)
- UI/UX packages (animations, icons, etc.)
- Platform-specific dependencies
- Development dependencies and build tools

**FLUTTER ENVIRONMENT CONFIGURATION:**
- Development environment setup instructions
- Flutter project configuration (pubspec.yaml)
- Platform-specific configurations (iOS Info.plist, Android manifest)
- Environment variables and configuration files
- Build configurations for different environments

**FLUTTER BUILD AND DEPLOYMENT:**
- Flutter build process and commands
- Platform-specific build requirements
- App store deployment procedures (iOS App Store, Google Play)
- Code signing and certificate management
- Testing framework and procedures (unit, widget, integration tests)

**MOBILE TECHNICAL CONSTRAINTS:**
- Performance requirements (app startup time, memory usage)
- Security requirements for mobile apps
- Platform compatibility requirements
- Device capability requirements (camera, GPS, etc.)
- Network connectivity handling

**FLUTTER DEVELOPMENT WORKFLOW:**
- Git workflow and branching strategy for Flutter projects
- Code review process for Flutter/Dart code
- Testing requirements before deployment
- Flutter-specific documentation requirements
- Widget testing and integration testing procedures

**MOBILE INFRASTRUCTURE:**
- App distribution (App Store, Google Play, internal testing)
- Backend infrastructure (Supabase/Firebase configuration)
- Analytics and crash reporting setup
- Push notification infrastructure
- Offline data synchronization strategy

**FLUTTER PROJECT STRUCTURE:**
- Feature-first folder organization
- Asset management (images, fonts, etc.)
- Localization and internationalization setup
- Platform-specific code organization
- Testing file organization

Save this as `memory_bank/tech_context.md`. Keep this document updated when Flutter dependencies change or new technical requirements are introduced.
```

##### 2.4.6 Enhanced Progress Tracking
Copy and paste this prompt:

```
Create a comprehensive Progress Tracking document that maintains a clear overview of project status and task management. Structure it as:

**COMPLETED FEATURES:**
- List of fully implemented and tested features
- Completion dates and responsible developers
- Any issues encountered during implementation
- Links to relevant commits or pull requests

**CURRENT TASKS:**
- Tasks currently being worked on
- Assigned developers and expected completion dates
- Current status and any blockers
- Dependencies and prerequisites

**PENDING TASKS:**
- Prioritized backlog of upcoming tasks
- Estimated effort and complexity
- Prerequisites and dependencies
- Target completion timeframes

**KNOWN ISSUES:**
- Bugs and issues currently identified
- Severity levels and impact assessment
- Workarounds currently in place
- Plans for resolution

**TESTING STATUS:**
- Features that have been tested
- Test coverage metrics
- Failed tests and their status
- Testing environments and procedures

**MILESTONE TRACKING:**
- Major milestones and their status
- Deadline tracking and risk assessment
- Dependencies between milestones
- Success criteria for each milestone

**TECHNICAL DEBT:**
- Identified technical debt items
- Impact assessment and priority
- Plans for addressing technical debt
- Refactoring opportunities

**NEXT SPRINT/ITERATION:**
- Tasks planned for next development cycle
- Goals and objectives
- Resource allocation
- Risk factors and mitigation plans

Save this as `memory_bank/progress_tracking.md`. Update this document regularly as tasks are completed and new work is identified. This should be the first document consulted when resuming work on the project.
```

#### 2.5 Review and Customize Your Memory Bank
After creating all enhanced files:

1. **Read through each file** to make sure it makes sense for your project
2. **Add any missing information** specific to your project
3. **Remove any sections** that don't apply to your project
4. **Ask your AI assistant** to clarify anything you don't understand

**Example question to ask your AI:**
```
I've created all the memory bank files, but I don't understand what "[specific term or concept]" means in the context of my project. Can you explain this in simple terms and give me an example for my [type of app] project?
```

---

## Step 3: Plan and Break Down Tasks Using AI Task Management

### What This Step Does
Creates a detailed plan for building your application by breaking it down into small, manageable tasks that can be completed one at a time.

### What You'll Do
1. Create a comprehensive Product Requirements Document (PRD)
2. Break down the project into specific tasks
3. Organize tasks in the right order
4. Review and refine the plan

### Detailed Instructions

#### 3.1 Generate Product Requirements Document (PRD)

**Copy and paste this exact Flutter-specific prompt:**

```
Acting as an experienced Flutter product manager, generate a comprehensive Product Requirements Document (PRD) for our Flutter mobile app that expands upon our existing Project Brief and Product Context. Structure the PRD as follows:

**EXECUTIVE SUMMARY:**
- Flutter mobile app vision and mission statement
- Key success metrics and KPIs for mobile users
- Target mobile market and user base
- Business objectives and ROI expectations for mobile app

**MOBILE USER STORIES AND PERSONAS:**
- Detailed mobile user personas with demographics and motivations
- Primary mobile user journeys mapped step-by-step
- User stories in "As a [mobile user], I want [goal] so that [benefit]" format
- Acceptance criteria for each user story (including mobile-specific criteria)
- Edge cases and error scenarios specific to mobile usage

**FLUTTER CORE FEATURES AND FUNCTIONALITY:**
- Feature prioritization (Must-have for MVP, Should-have, Could-have, Won't-have)
- Detailed Flutter widget specifications with UI descriptions
- Mobile user interface requirements and interaction patterns
- Data requirements and business logic for each feature
- Integration points with device features (camera, GPS, notifications, etc.)
- Offline functionality requirements for each feature

**FLUTTER TECHNICAL REQUIREMENTS:**
- Performance requirements (app startup time, memory usage, battery efficiency)
- Security and compliance requirements for mobile apps
- Scalability requirements for user growth
- Platform compatibility matrix (iOS/Android versions)
- API specifications and data formats
- State management requirements with Riverpod
- Local database requirements with Drift

**MOBILE NON-FUNCTIONAL REQUIREMENTS:**
- Accessibility standards for mobile (screen readers, large text, voice control)
- Internationalization and localization needs
- Analytics and crash reporting requirements
- Push notification specifications
- Background processing requirements

**FLUTTER SUCCESS CRITERIA:**
- Definition of MVP (Minimum Viable Product) for mobile
- User acceptance criteria for mobile experience
- Quality assurance standards for Flutter apps
- App store launch readiness checklist (iOS App Store, Google Play)
- Performance benchmarks for mobile devices

**MOBILE RISK ASSESSMENT:**
- Technical risks specific to Flutter development and mitigation strategies
- Platform-specific risks (iOS vs Android) and contingency plans
- App store approval risks and buffer strategies
- Device compatibility risks and alternatives
- Network connectivity risks and offline strategies

**FLUTTER DEVELOPMENT CONSIDERATIONS:**
- Widget architecture and composition strategies
- State management patterns with Riverpod
- Navigation and routing requirements
- Platform-specific adaptations needed
- Testing strategy (unit, widget, integration tests)

Reference our existing memory_bank/project_brief.md and memory_bank/product_context.md files to ensure consistency. Save this as memory_bank/prd.md and ensure it serves as the definitive product specification for Flutter development teams.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Copy the AI's response
2. Create a new file called `prd.md` in your `memory_bank` folder
3. Paste the content and save the file

#### 3.2 Break Down Into Manageable Tasks

**Copy and paste this exact Flutter-specific prompt:**

```
Based on our comprehensive Flutter PRD, break down the entire Flutter project into smaller, modular, and manageable tasks. This breakdown is critical for sequential Flutter development and dependency management. Structure the breakdown as follows:

**FLUTTER DEPENDENCY ANALYSIS:**
- Map all Flutter feature dependencies and prerequisites
- Identify critical path items that block other Flutter features
- Note shared Flutter widgets and components that multiple features depend on
- Highlight integration points with device features and backend services
- Identify Riverpod provider dependencies

**FLUTTER TASK CATEGORIZATION:**
- Flutter project setup and configuration tasks
- Drift database and data model tasks
- Backend integration tasks (Supabase/Firebase)
- Flutter widget and UI component tasks
- Riverpod state management tasks
- Testing tasks (unit, widget, integration)
- Platform-specific tasks (iOS/Android)
- App store deployment tasks

**DETAILED FLUTTER TASK BREAKDOWN:**
For each task, provide:
- Task title and unique identifier
- Detailed description and acceptance criteria
- Estimated effort (hours/days) and complexity level
- Prerequisites and dependencies (what must be done first)
- Deliverables and success criteria
- Potential risks and blockers
- Flutter files/widgets that will be created or modified
- Riverpod providers that will be created or modified

**FLUTTER SPRINT/ITERATION PLANNING:**
- Group tasks into logical Flutter development phases
- Prioritize tasks based on mobile user value and dependencies
- Identify tasks that can be worked on in parallel
- Suggest optimal Flutter development sequence
- Consider platform-specific development timing

**FLUTTER MVP DEFINITION:**
- Clearly identify which tasks are required for Flutter MVP
- Separate nice-to-have features for post-MVP iterations
- Define MVP success criteria and Flutter testing requirements
- Specify minimum platform support for MVP

Create tasks in order of Flutter implementation priority, ensuring that no task depends on something that comes later in the sequence. Add all tasks to memory_bank/progress_tracking.md with status "pending" and update the task breakdown section with this comprehensive analysis.

Example Flutter task format:
```
TASK-001: Flutter Project Setup
- Description: Initialize Flutter project with proper folder structure and dependencies
- Dependencies: None (starting task)
- Effort: 1 day
- Files: pubspec.yaml, main.dart, lib/ folder structure
- Acceptance Criteria: Flutter project runs on both iOS and Android emulators

TASK-002: User Authentication UI
- Description: Create login and signup Flutter widgets with form validation
- Dependencies: Flutter Project Setup (TASK-001)
- Effort: 2-3 days
- Files: lib/features/auth/presentation/login_screen.dart, signup_screen.dart
- Riverpod Providers: authProvider, loginFormProvider
- Acceptance Criteria: Users can enter credentials, see validation errors, navigate between screens
```

Ensure every feature from the Flutter PRD is covered by at least one task, and every task contributes to a feature in the PRD.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Copy the task breakdown from the AI's response
2. Open your `memory_bank/progress_tracking.md` file
3. Add the task breakdown to the appropriate section
4. Save the file

#### 3.3 Review and Refine Plan

**Copy and paste this exact prompt:**

```
Conduct a comprehensive review of our current project plan and task breakdown. This is a critical checkpoint to ensure our plan is realistic, complete, and aligned with our goals. Focus on:

**COMPLETENESS ASSESSMENT:**
- Verify every feature in the PRD is covered by tasks
- Check that all non-functional requirements are addressed
- Ensure testing, deployment, and documentation tasks are included
- Validate that setup and infrastructure tasks are comprehensive
- Confirm that integration tasks between components are planned

**DEPENDENCY VALIDATION:**
- Review all task dependencies for accuracy
- Identify any circular dependencies that need resolution
- Ensure the critical path is clearly identified
- Validate that parallel work streams don't conflict
- Check that shared components are built before dependent features

**EFFORT ESTIMATION REVIEW:**
- Assess if task estimates are realistic based on complexity
- Identify tasks that might be too large and need further breakdown
- Consider if any tasks are too small and can be combined
- Account for testing, debugging, and integration time
- Include buffer time for unexpected issues

**RISK ANALYSIS:**
- Identify high-risk tasks that might cause delays
- Plan mitigation strategies for technical risks
- Consider alternative approaches for complex features
- Identify external dependencies that could cause delays
- Plan contingency options for critical path items

**REFINEMENT RECOMMENDATIONS:**
- Suggest task reordering for better development flow
- Recommend task breakdown refinements
- Identify opportunities for parallel development
- Suggest prototype or proof-of-concept tasks for risky features
- Recommend early integration checkpoints

**FEEDBACK INTEGRATION:**
Based on this review, provide specific recommendations for:
- Tasks that should be broken down further
- Dependencies that need clarification or adjustment
- Estimates that seem unrealistic
- Missing tasks or requirements
- Alternative implementation approaches to consider

Update memory_bank/progress_tracking.md with refined task breakdown and memory_bank/active_context.md with key decisions and next steps from this review.
```

**What to do with the response:**
1. Read through the AI's recommendations carefully
2. Update your `memory_bank/progress_tracking.md` file with any suggested changes
3. Update your `memory_bank/active_context.md` file with key decisions
4. If you have questions about any recommendations, ask your AI assistant for clarification

---

## Step 4: Implement Features Using Test-Driven Development (TDD)

### What This Step Does
Builds your application one feature at a time using a proven method called Test-Driven Development (TDD). This approach writes tests first, then writes code to pass those tests, ensuring high-quality, working code.

### What You'll Do
1. Choose the first task from your plan
2. Create a comprehensive implementation plan with package selection
3. Get user approval before starting implementation
4. Execute complete TDD cycle in one shot (tests → implementation → refinement)
5. Update your progress tracking

### Detailed Instructions

#### 4.1 One-Shot TDD Implementation

**Copy and paste this streamlined Flutter TDD prompt:**

```
**FLUTTER TDD IMPLEMENTATION**

Execute complete TDD workflow for our next priority task. WAIT FOR USER APPROVAL before generating code.

**PHASE 1: PLANNING & ANALYSIS**

**Setup:**
1. Read memory_bank/progress_tracking.md for next priority task
2. Review memory_bank/system_patterns.md for coding standards
3. Check memory_bank/active_context.md for implementation guidance
4. Validate prerequisites are completed

**Implementation Plan:**
- Required packages and dependencies (exact versions)
- Widget hierarchy and Riverpod providers needed
- Files to create (with paths)
- Testing strategy (widget, unit, integration tests)
- Modern Flutter 2025+ compliance (Color.withValues, @riverpod, Material Design 3)

**WAIT FOR USER APPROVAL:** Ask "Do you approve this plan? Should I proceed with TDD implementation?"

**PHASE 2: TDD IMPLEMENTATION (After approval)**

**Step 1: Create Failing Tests**
- Widget tests for UI components
- Unit tests for Riverpod providers
- Integration tests for user flows
- Error handling and edge cases

**Step 2: Minimal Implementation**
- Write minimal code to pass tests
- Follow established patterns
- Use modern Flutter APIs
- Implement proper error handling

**Step 3: Refactor & Optimize**
- Improve code quality
- Optimize performance
- Ensure Flutter 2025+ compliance
- Validate all tests pass

**Deliverables:**
- Complete test suite
- Working implementation
- Updated dependencies
- Documentation

IMPORTANT: Use modern Flutter 2025+ standards:
- Color.withValues() instead of Color.withOpacity()
- Riverpod code generation (@riverpod) instead of StateNotifier
- Material Design 3 components
- No deprecated methods
```

**What to do with the response:**
1. Review the AI's comprehensive implementation plan
2. Approve or request modifications to the plan
3. Once approved, the AI will execute the complete TDD cycle
4. Review the final implementation and tests


#### 4.2 Task Completion and Status Update

**Copy and paste this exact prompt:**

```
Now that we've completed [task name], please:

1. Run a final test suite to ensure everything works
2. Update our Progress Tracking file to mark this task as complete
3. Update Active Context with what we accomplished and any new patterns discovered
4. Identify the next priority task from our list
5. Note any new dependencies or issues that emerged

Provide a brief summary of what was implemented and any architectural decisions made.
```

**What to do with the response:**
1. Update your `memory_bank/progress_tracking.md` file to mark the task as complete
2. Update your `memory_bank/active_context.md` file with what you accomplished
3. Note which task you should work on next
4. Repeat the TDD process (steps 4.1-4.2) for the next task





---

## Step 5: Critical Code Review and Debugging

### What This Step Does
Reviews all the code you've built to catch problems, improve quality, and fix any bugs. This step ensures your application is reliable and secure.

### What You'll Do
1. Have AI review your code for problems
2. Fix any issues found
3. Debug problems when they occur
4. Get help when you're stuck

### Detailed Instructions

#### 5.1 Systematic Code Review

After completing each feature (or every few features), perform a comprehensive code review.

**Copy and paste this exact Flutter code review prompt:**

```
Please perform a thorough Flutter code review of the code we just implemented. Act as a senior Flutter developer reviewing a junior's pull request.

Check for Flutter-specific issues:
- **Security vulnerabilities** (input validation, data exposure, platform-specific security)
- **Performance issues** (unnecessary widget rebuilds, memory leaks, inefficient state management)
- **Flutter best practices** (widget composition, proper use of keys, lifecycle management)
- **Mobile-specific considerations** (battery usage, network efficiency, offline handling)
- **Edge cases** (null values, empty lists, network failures, device orientation changes)
- **Error handling** (try-catch blocks, meaningful error messages, graceful degradation)
- **Code quality** (readability, maintainability, adherence to Flutter best practices)
- **Test coverage** (widget tests, unit tests, integration tests coverage)
- **Riverpod usage** (proper provider usage, state management patterns)
- **Platform compatibility** (iOS vs Android considerations)

Provide specific Flutter feedback with suggested improvements for any issues found.

For each issue you identify:
1. Explain why it's a problem in Flutter/mobile context
2. Show the specific Flutter code that needs to be changed
3. Provide the corrected Flutter version
4. Explain how the fix improves the Flutter app

Focus on issues that could cause:
- Security problems in mobile apps
- App crashes or ANRs (Application Not Responding)
- Poor mobile user experience
- Battery drain or performance issues
- Difficulty maintaining the Flutter code in the future
- App store rejection issues

**Flutter-Specific Review Areas:**
- Widget lifecycle and disposal
- State management with Riverpod
- Navigation and routing
- Platform-specific adaptations
- Accessibility compliance
- Responsive design for different screen sizes
- Proper use of async/await in Flutter context

**Modern Flutter Standards Checklist (2025+):**
- [ ] **Color Management**: Uses `Color.withValues()` instead of deprecated `Color.withOpacity()`
- [ ] **State Management**: Uses Riverpod code generation (`@riverpod`) instead of `StateNotifier`
- [ ] **Material Design**: Uses Material Design 3 components where available
- [ ] **Widget Patterns**: Uses modern widget constructors and patterns
- [ ] **Dependencies**: All packages are current versions without deprecated APIs
- [ ] **Dart Features**: Uses current Dart language features and syntax
- [ ] **No Deprecated Methods**: Code doesn't use any deprecated Flutter or Dart methods
- [ ] **No Deprecated Packages**: Dependencies don't include deprecated packages
- [ ] **No Deprecated Patterns**: Code follows current architectural patterns
- [ ] **Future-Proof**: Code uses stable APIs that won't be deprecated soon

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Read through all the AI's feedback carefully
2. For each issue identified:
   - Replace the problematic code with the AI's suggested fix
   - Update your test files if needed
   - Run tests to make sure the fixes work
3. Ask questions if you don't understand any of the feedback

**Example follow-up question:**
```
I don't understand why [specific issue] is a problem. Can you explain this in simpler terms and show me a specific example of how this could cause issues for users of my app?
```

#### 5.2 Debugging AI-Generated Code

When you encounter errors or problems with your code, use this systematic debugging approach.

**Copy and paste this exact prompt template** (fill in the bracketed sections with your specific information):

```
I'm encountering an issue with the code you generated:

**Code Section:**
```[language]
[paste the problematic code]
```

**Error Message:**
```
[paste exact error message]
```

**Expected Behavior:**
[describe what should happen]

**Actual Behavior:**
[describe what actually happens]

**Context:**
[any relevant context about when/how the error occurs]

Please:
1. Explain step-by-step what you think is causing this issue
2. Provide a corrected version of the code
3. Explain why your fix addresses the root cause
4. Suggest additional tests to prevent similar issues
5. Help me understand how to avoid this type of problem in the future

Break down your explanation so someone with no programming experience can understand what went wrong and why the fix works.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**Example of a completed debugging prompt:**
```
I'm encountering an issue with the code you generated:

**Code Section:**
```javascript
function addTask(taskName) {
  const task = {
    id: Date.now(),
    name: taskName,
    completed: false
  };
  tasks.push(task);
  return task;
}
```

**Error Message:**
```
ReferenceError: tasks is not defined
```

**Expected Behavior:**
The function should add a new task to the list and return the task object.

**Actual Behavior:**
The function crashes with an error saying "tasks is not defined".

**Context:**
This error occurs when I try to call addTask("Buy groceries") in my application.

Please:
1. Explain step-by-step what you think is causing this issue
2. Provide a corrected version of the code
3. Explain why your fix addresses the root cause
4. Suggest additional tests to prevent similar issues
5. Help me understand how to avoid this type of problem in the future

Break down your explanation so someone with no programming experience can understand what went wrong and why the fix works.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Read the AI's explanation carefully
2. Apply the suggested fix to your code
3. Test the fix to make sure it works
4. Add any suggested tests to prevent the problem from happening again
5. Update your memory bank files if the AI suggests any pattern changes

#### 5.3 Getting Unstuck

When you're stuck on a problem and the usual approaches aren't working, use this creative problem-solving approach.

**Copy and paste this exact prompt template** (fill in the bracketed sections):

```
We seem to be stuck on [specific problem]. Let's step back and think creatively.

Current approach: [describe current approach]
Issue: [describe what's not working]

Please:
1. Suggest 2-3 completely different approaches to solve this problem
2. Explain the pros and cons of each approach
3. Recommend which approach to try first and why
4. Identify any assumptions we might be making that could be wrong
5. Provide step-by-step instructions for implementing your recommended approach

Let's explore alternative solutions before continuing with the current approach. Explain everything in terms a beginner can understand.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**Example of a completed problem-solving prompt:**
```
We seem to be stuck on implementing user authentication for our task management app. Let's step back and think creatively.

Current approach: Trying to build a custom authentication system from scratch with username/password storage.
Issue: The code is getting very complex and we're worried about security issues with storing passwords.

Please:
1. Suggest 2-3 completely different approaches to solve this problem
2. Explain the pros and cons of each approach
3. Recommend which approach to try first and why
4. Identify any assumptions we might be making that could be wrong
5. Provide step-by-step instructions for implementing your recommended approach

Let's explore alternative solutions before continuing with the current approach. Explain everything in terms a beginner can understand.

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

**What to do with the response:**
1. Read through all the alternative approaches
2. Ask questions about any approaches you don't understand
3. Choose the approach that seems best for your situation
4. Update your memory bank files with the new approach
5. Update your task list if the new approach requires different tasks

#### 5.4 Continuous Quality Improvement

**Copy and paste this prompt periodically** (every few completed features):

```
Let's do a comprehensive quality check of our entire codebase so far. Please:

**OVERALL CODE REVIEW:**
1. Review all the code we've written for consistency
2. Identify any patterns that should be standardized
3. Look for opportunities to reduce code duplication
4. Check that our coding standards are being followed consistently

**ARCHITECTURE REVIEW:**
1. Assess if our current architecture is still appropriate
2. Identify any architectural improvements we should make
3. Check if components are properly separated and organized
4. Suggest any refactoring that would improve the codebase

**TESTING REVIEW:**
1. Evaluate our test coverage
2. Identify any gaps in testing
3. Suggest additional tests for better coverage
4. Review test quality and organization

**DOCUMENTATION REVIEW:**
1. Check if our memory bank files are up to date
2. Identify any missing documentation
3. Suggest improvements to our documentation
4. Ensure new team members could understand the project

**NEXT STEPS:**
Based on this review, provide a prioritized list of improvements we should make before continuing with new features.

Update our memory_bank files with any important findings from this review.
```

**What to do with the response:**
1. Review all the AI's suggestions
2. Prioritize which improvements are most important
3. Add improvement tasks to your progress tracking
4. Update your memory bank files with any new patterns or decisions
5. Implement the highest-priority improvements before continuing with new features

---

## Step 6: Maintain Context and Continuously Improve

### What This Step Does
Keeps your project organized and your AI assistant informed about your progress. This step also helps you learn from what you've built and continuously improve your application.

### What You'll Do
1. Update your memory bank files regularly
2. Learn about the concepts you've implemented
3. Improve and optimize your code
4. Plan future enhancements

### Detailed Instructions

#### 6.1 Regular Memory Bank Updates

**Update your memory bank after every 2-3 completed tasks** or at the end of each development session.

**Copy and paste this exact prompt:**

```
Maintain the Memory Bank with real-time project status and context. This continuous updating is essential for maintaining AI context across development sessions. Focus on:

**PROGRESS TRACKING UPDATES:**
- Mark completed tasks with completion date and any notes
- Update task status (pending, in-progress, completed, blocked)
- Add new tasks discovered during implementation
- Update effort estimates based on actual time spent
- Note any changes in task dependencies or requirements

**ACTIVE CONTEXT MAINTENANCE:**
- Document current development focus and priorities
- Record recent architectural decisions and their rationale
- Note any emerging patterns or code conventions
- Track any blockers or issues requiring attention
- Update next steps and immediate priorities

**PATTERN DOCUMENTATION:**
- Document new architectural patterns as they emerge
- Update coding standards based on implementation experience
- Record component design patterns and reusable solutions
- Note any anti-patterns discovered and avoided
- Update integration patterns and API conventions

**TECHNICAL CONTEXT UPDATES:**
- Track new dependencies or library additions
- Update environment configuration changes
- Document new tools or development processes adopted
- Record performance optimizations and their impact
- Update deployment or build process changes

**AUTOMATED UPDATE TRIGGERS:**
Trigger memory bank updates when:
- Completing any development task
- Making significant architectural decisions
- Encountering and resolving blockers
- Adding new dependencies or tools
- Discovering new patterns or best practices
- Starting work on a new feature area

**UPDATE TEMPLATE:**
```
MEMORY BANK UPDATE - [DATE]
=========================

COMPLETED WORK:
- [Task completed with brief description]
- [Any issues encountered and resolved]

CURRENT STATUS:
- [What's currently being worked on]
- [Any blockers or dependencies waiting]

NEW PATTERNS/DECISIONS:
- [Any new architectural or coding patterns established]
- [Reasoning behind significant decisions]

NEXT PRIORITIES:
- [Immediate next tasks]
- [Any preparation needed for upcoming work]

NOTES FOR FUTURE AI SESSIONS:
- [Specific guidance for maintaining consistency]
- [Areas requiring special attention]
```

This systematic updating ensures that every AI interaction has full context of the project's current state and history.
```

**What to do with the response:**
1. Use the AI's response to update your memory bank files
2. Open each relevant file (`active_context.md`, `progress_tracking.md`, etc.)
3. Add the new information to the appropriate sections
4. Save all updated files

#### 6.2 Learning and Knowledge Transfer

**Use this prompt to understand concepts you've implemented** (great for learning!):

```
Help me understand [specific concept/algorithm/pattern] that we just implemented.

Please explain:
1. **What it does** (high-level purpose)
2. **How it works** (step-by-step breakdown)
3. **Why we chose this approach** (alternatives and trade-offs)
4. **When to use it** (similar scenarios where this pattern applies)
5. **Potential improvements** (how we could make it better)

Provide examples and analogies to help me fully grasp the concept. Explain everything in terms a beginner can understand, avoiding technical jargon where possible.

Also, suggest resources (articles, tutorials, videos) where I can learn more about this concept.
```

**Example of a completed learning prompt:**
```
Help me understand the "Model-View-Controller (MVC) pattern" that we just implemented in our task management app.

Please explain:
1. **What it does** (high-level purpose)
2. **How it works** (step-by-step breakdown)
3. **Why we chose this approach** (alternatives and trade-offs)
4. **When to use it** (similar scenarios where this pattern applies)
5. **Potential improvements** (how we could make it better)

Provide examples and analogies to help me fully grasp the concept. Explain everything in terms a beginner can understand, avoiding technical jargon where possible.

Also, suggest resources (articles, tutorials, videos) where I can learn more about this concept.
```

**What to do with the response:**
1. Read through the explanation carefully
2. Ask follow-up questions about anything you don't understand
3. Save the explanation in a "learning_notes.md" file for future reference
4. Check out any recommended resources when you have time

#### 6.3 Refactoring and Optimization

**Use this prompt every 5-10 completed tasks** to improve your codebase:

```
Let's review our codebase for refactoring opportunities. Please:

1. **Identify code smells** (duplicated code, long functions, unclear naming)
2. **Suggest architectural improvements** (better separation of concerns, design patterns)
3. **Recommend performance optimizations** (without premature optimization)
4. **Highlight testing gaps** (areas that need better test coverage)

For each suggestion:
- Explain why it's an improvement
- Show the current code and the improved version
- Estimate the effort required to make the change
- Explain the benefits of making the change

Prioritize suggestions by impact and implementation effort. Focus on improvements that will:
- Make the code easier to understand and maintain
- Improve application performance
- Reduce the likelihood of bugs
- Make it easier to add new features

After major refactoring, we'll need to update our memory bank files with any new patterns or architectural decisions.
```

**What to do with the response:**
1. Review all the AI's suggestions
2. Prioritize which improvements to make first
3. Add refactoring tasks to your progress tracking
4. Implement the highest-priority improvements
5. Update your memory bank files with any new patterns

#### 6.4 Planning Future Enhancements

**Use this prompt when you've completed your MVP** or want to plan next steps:

```
Now that we've completed [current milestone/MVP], let's plan future enhancements and improvements. Please:

**FEATURE ENHANCEMENT PLANNING:**
1. Review our original project goals and assess what we've achieved
2. Identify the most valuable features to add next
3. Suggest improvements to existing features based on user feedback patterns
4. Recommend new features that would significantly improve user experience

**TECHNICAL IMPROVEMENT PLANNING:**
1. Identify technical debt that should be addressed
2. Suggest performance improvements and optimizations
3. Recommend security enhancements
4. Plan for scalability improvements

**USER EXPERIENCE IMPROVEMENTS:**
1. Suggest UI/UX enhancements
2. Recommend accessibility improvements
3. Plan for mobile responsiveness improvements
4. Suggest user onboarding improvements

**MAINTENANCE AND OPERATIONS:**
1. Plan for monitoring and analytics implementation
2. Suggest backup and disaster recovery improvements
3. Recommend documentation improvements
4. Plan for automated testing enhancements

For each suggestion, provide:
- Clear description of the improvement
- Expected benefits for users
- Estimated effort required
- Priority level (High/Medium/Low)
- Dependencies on other improvements

Create a roadmap for the next 3-6 months of development, organized by priority and dependencies.
```

**What to do with the response:**
1. Review the AI's roadmap suggestions
2. Prioritize based on your goals and available time
3. Add high-priority items to your progress tracking
4. Update your project brief with new goals if needed
5. Start working on the highest-priority improvements

#### 6.5 Knowledge Documentation

**Create a project knowledge base** by using this prompt periodically:

```
Help me create comprehensive documentation for our project that would help a new developer (or future me) understand and work with this codebase. Please create:

**DEVELOPER ONBOARDING GUIDE:**
1. How to set up the development environment
2. How to run the application locally
3. How to run tests
4. How to deploy the application
5. Overview of the codebase structure

**FEATURE DOCUMENTATION:**
1. Description of each major feature
2. How each feature works technically
3. Key files and components for each feature
4. Known limitations or issues

**API DOCUMENTATION:**
1. List of all API endpoints (if applicable)
2. Request/response formats
3. Authentication requirements
4. Error handling

**TROUBLESHOOTING GUIDE:**
1. Common issues and their solutions
2. How to debug problems
3. Performance optimization tips
4. Security considerations

**FUTURE DEVELOPMENT GUIDE:**
1. How to add new features
2. Coding standards and patterns to follow
3. Testing requirements
4. Deployment procedures

Save this documentation in a `docs` folder in our project, organized into separate files for each section.
```

**What to do with the response:**
1. Create a `docs` folder in your project
2. Save each section as a separate `.md` file
3. Keep this documentation updated as you add new features
4. Use this documentation when you return to the project after a break

---

---

## Best Practices and Tips for Beginners

### How to Work Effectively with AI

#### 1. Communication Tips
- **Be Specific**: Instead of "make it better," say "make the button bigger and blue"
- **Provide Context**: Always mention what you're trying to achieve
- **Use Examples**: Show the AI exactly what you want with concrete examples
- **Ask Questions**: If you don't understand something, ask for clarification

#### 2. Managing Your Project
- **Start Small**: Begin with simple features and gradually add complexity
- **Save Everything**: Keep all your files backed up and organized
- **Update Regularly**: Keep your memory bank files current
- **Test Often**: Check that your app works after each change

#### 3. Learning as You Go
- **Ask "Why"**: Don't just copy code - understand why it works
- **Take Notes**: Keep a learning journal of new concepts
- **Practice**: Try modifying code to see what happens
- **Research**: Look up concepts you don't understand

### Troubleshooting Common Issues

#### When AI Gives Confusing Responses
```
I don't understand your response. Can you:
1. Explain this in simpler terms
2. Break it down into smaller steps
3. Give me a specific example
4. Tell me exactly what I should do next
```

#### When Code Doesn't Work
```
The code you provided isn't working. Here's what's happening:
- What I expected: [describe expected behavior]
- What actually happened: [describe actual behavior]
- Error message (if any): [paste exact error]

Please help me fix this step by step.
```

#### When You're Overwhelmed
```
I'm feeling overwhelmed by all the information. Can you:
1. Identify the most important next step
2. Ignore the advanced features for now
3. Focus on just getting the basic functionality working
4. Give me a simple checklist of what to do next
```

#### When You Want to Learn More
```
I want to understand [specific concept] better. Can you:
1. Explain it like I'm 10 years old
2. Give me a real-world analogy
3. Show me a very simple example
4. Recommend beginner-friendly resources to learn more
```

### Project Organization Tips

#### Flutter Project Organization
```
my_flutter_app/
├── memory_bank/           # All your planning documents
│   ├── project_brief.md
│   ├── product_context.md
│   ├── active_context.md
│   ├── system_patterns.md
│   ├── tech_context.md
│   ├── progress_tracking.md
│   └── prd.md
├── docs/                  # Documentation
├── lib/                   # Flutter application code
│   ├── core/             # Shared utilities and constants
│   ├── features/         # Feature-first organization
│   │   ├── auth/         # Authentication feature
│   │   ├── tasks/        # Task management feature
│   │   └── profile/      # User profile feature
│   ├── shared/           # Shared widgets and providers
│   └── main.dart         # App entry point
├── test/                  # Flutter test files
├── android/              # Android-specific files
├── ios/                  # iOS-specific files
├── pubspec.yaml          # Flutter dependencies
└── README.md             # Project overview
```

#### Backup Strategy
1. **Save frequently** - Don't lose your work!
2. **Use cloud storage** - Google Drive, Dropbox, etc.
3. **Keep multiple versions** - Save copies before major changes
4. **Export your memory bank** - These files are crucial for continuing work

### Success Strategies

#### 1. Set Realistic Expectations
- **Start with a simple app** - Don't try to build Facebook on your first try
- **Expect to make mistakes** - Everyone does, even experienced developers
- **Celebrate small wins** - Getting any feature working is an achievement
- **Be patient** - Learning takes time

#### 2. Build Good Habits
- **Work in small chunks** - 30-60 minutes at a time
- **Take breaks** - Your brain needs rest to process new information
- **Keep notes** - Write down what you learn
- **Ask for help** - Use your AI assistant liberally

#### 3. Focus on Learning
- **Understand, don't just copy** - Ask why things work the way they do
- **Experiment** - Try changing things to see what happens
- **Read your code out loud** - This helps you understand what it does
- **Teach someone else** - Explaining concepts helps you learn them better

---

## Quick Reference: Essential Prompts

### Starting a New Flutter Session
```
I'm continuing work on my Flutter [type of app] project. Please read my memory_bank/active_context.md file to understand where we left off, then tell me:
1. What Flutter feature we were working on last
2. What the next priority Flutter task is
3. Any blockers or issues I should be aware of
4. What I should do to get started with Flutter development today
5. Any Flutter-specific setup I need to check (emulator, dependencies, etc.)

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

### When You're Stuck on Flutter Issues
```
I'm stuck on [specific Flutter problem]. Can you:
1. Help me understand what's going wrong with my Flutter code
2. Suggest 2-3 different Flutter approaches to solve this
3. Recommend which Flutter approach to try first
4. Give me step-by-step instructions for that approach
5. Consider any platform-specific (iOS/Android) implications

IMPORTANT: Ensure all Flutter code follows current 2025+ standards and best practices. Use modern Flutter APIs and avoid deprecated methods. Specifically:
- Use Color.withValues() instead of Color.withOpacity()
- Use Riverpod code generation (@riverpod) instead of StateNotifier
- Use current Material Design 3 components
- Verify all dependencies and methods are not deprecated
- If unsure about current best practices, ask for clarification on the most modern approach
```

### Before Ending a Flutter Session
```
Before I stop working on my Flutter app today, please help me:
1. Update my memory_bank/active_context.md with what Flutter features we accomplished
2. Update my memory_bank/progress_tracking.md with completed Flutter tasks
3. Identify what Flutter feature I should work on next time
4. Note any important Flutter decisions or patterns we established today
5. Ensure my Flutter project is in a good state for next session
```

### Getting Back on Track with Flutter
```
I feel like I've lost track of my Flutter project goals. Can you:
1. Review my memory_bank/project_brief.md
2. Assess what Flutter features we've accomplished so far
3. Remind me of the original mobile app vision
4. Help me refocus on the most important next Flutter steps
5. Prioritize remaining Flutter features for mobile users
```

---

## Workflow Summary Checklist

### Before You Start
- [ ] Have a clear idea of what you want to build
- [ ] Set up your project folder structure
- [ ] Choose an AI assistant to work with

### Step 1: Define Your Flutter Vision (1-2 hours)
- [ ] Write down your mobile app idea and goals
- [ ] Use AI to refine and organize your Flutter vision
- [ ] Understand who will use your mobile app and why
- [ ] Consider mobile-specific features and constraints

### Step 2: Set Up Flutter Memory Bank (2-3 hours)
- [ ] Create memory_bank folder structure
- [ ] Generate all 6 Flutter memory bank files using enhanced prompts
- [ ] Review and customize files for your Flutter project
- [ ] Understand what each file is for in Flutter context

### Step 3: Plan Your Flutter Project (3-4 hours)
- [ ] Create comprehensive Flutter Product Requirements Document (PRD)
- [ ] Break down Flutter project into specific tasks
- [ ] Organize Flutter tasks in the right order
- [ ] Review and refine the Flutter plan
- [ ] Choose backend (Supabase vs Firebase)

### Step 4: Build Flutter Features (Ongoing)
For each Flutter feature:
- [ ] Choose next priority Flutter task
- [ ] Create comprehensive implementation plan with package selection
- [ ] Get user approval before starting implementation
- [ ] Execute complete TDD cycle in one shot (tests → implementation → refinement)
- [ ] Update progress tracking

### Step 5: Review and Debug Flutter Code (After each feature)
- [ ] Have AI review your Flutter code for problems
- [ ] Fix any Flutter-specific issues found
- [ ] Debug Flutter problems when they occur
- [ ] Get help when stuck on Flutter issues

### Step 6: Maintain and Improve Flutter App (Weekly)
- [ ] Update memory bank files with Flutter progress
- [ ] Learn about Flutter concepts you've implemented
- [ ] Improve and optimize Flutter code
- [ ] Plan future Flutter enhancements

### Flutter Project Completion
- [ ] All MVP Flutter features working on both platforms
- [ ] Flutter code reviewed and tested
- [ ] Documentation complete
- [ ] Flutter app ready for app store submission
- [ ] iOS and Android builds successful

---

## Final Tips for Flutter Success

1. **Start Today**: The best way to learn Flutter is by building
2. **Be Patient**: Building mobile apps takes time, even with AI help
3. **Stay Curious**: Ask questions about Flutter concepts and explore how widgets work
4. **Celebrate Progress**: Every working Flutter feature is an achievement
5. **Keep Learning**: Each Flutter project teaches you something new about mobile development
6. **Test on Real Devices**: Use both emulators and physical devices for testing
7. **Follow Flutter Best Practices**: Stick to the architectural guidelines in this guide
8. **Think Mobile-First**: Always consider the mobile user experience

### Flutter-Specific Success Factors

- **Master Widget Composition**: Learn to build complex UIs from simple widgets
- **Understand State Management**: Riverpod will become your best friend
- **Embrace Hot Reload**: Use Flutter's fast development cycle to your advantage
- **Plan for Both Platforms**: Consider iOS and Android differences from the start
- **Focus on Performance**: Mobile users expect fast, responsive apps
- **Design for Touch**: Remember that mobile users interact with fingers, not mice

Remember: You don't need to be a programmer to build Flutter mobile apps with AI assistance. This guide gives you everything you need to get started with Flutter development. The most important step is the first one - so begin your Flutter journey today!

### Getting Started Right Now

1. **Install Flutter**: Follow the official Flutter installation guide for your operating system
2. **Set up your IDE**: Install VS Code or Android Studio with Flutter extensions
3. **Create your first Flutter project**: Run `flutter create my_first_app`
4. **Start with this guide**: Begin with Step 1 and follow each step in order

Your Flutter mobile app journey starts now! 🚀

---

*This guide combines the best practices from AI pair programming workflows and enhanced memory bank systems, specifically tailored for Flutter mobile app development. It provides a comprehensive, beginner-friendly approach to building Flutter applications with AI assistance.*