# MeditatingLeo Flutter System Patterns

This document outlines the architectural decisions, coding standards, and design patterns used in the MeditatingLeo Flutter ecosystem. It ensures consistency across all three independent applications: mobile app, web app, and admin panel.

## Table of Contents
1. [Architectural Overview](#flutter-architectural-overview)
2. [Modern Flutter 2025+ Standards](#modern-flutter-2025-standards)
3. [Code Organization](#flutter-code-organization)
4. [Naming Conventions](#naming-conventions)
5. [Design Patterns](#flutter-design-patterns)
6. [Component Relationships](#flutter-component-relationships)
7. [Error Handling](#error-handling)
8. [Cross-Platform Considerations](#cross-platform-considerations)
9. [Testing Standards](#testing-standards)
10. [Performance Guidelines](#performance-guidelines)
11. [Security Guidelines](#security-guidelines)
12. [Documentation Standards](#documentation-standards)
13. [Import Organization](#import-organization)
14. [Anti-Patterns to Avoid](#flutter-anti-patterns-to-avoid)
15. [Mobile-Specific Patterns](#mobile-specific-patterns)

---

## FLUTTER ARCHITECTURAL OVERVIEW

### High-Level Architecture Philosophy
- **Feature-First Organization**: Code organized by business features rather than technical layers
- **Independent Applications**: Three standalone Flutter apps with no shared code packages
- **Modern Flutter 2025+ Standards**: Using latest APIs and avoiding deprecated methods
- **Material Design 3**: Consistent UI/UX across all platforms with platform-specific adaptations
- **Offline-First**: Local-first architecture with Drift database and Supabase sync

### Feature-First Directory Structure

Each application (`meditatingleo_app`, `meditatingleo_webapp`, `meditatingleo_admin`) must follow this structure:

```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   └── router/
│       ├── app_router.dart
│       └── routes.dart
├── core/
│   ├── constants/
│   │   ├── app_constants.dart
│   │   ├── api_constants.dart
│   │   └── ui_constants.dart
│   ├── extensions/
│   │   ├── context_extensions.dart
│   │   ├── string_extensions.dart
│   │   └── date_extensions.dart
│   ├── theme/
│   │   ├── app_theme.dart
│   │   ├── color_scheme.dart
│   │   └── text_theme.dart
│   ├── utils/
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── helpers.dart
│   └── widgets/
│       ├── loading_widget.dart
│       ├── error_widget.dart
│       └── custom_button.dart
├── features/
│   ├── authentication/
│   │   ├── data/
│   │   │   ├── models/
│   │   │   │   ├── user_model.dart
│   │   │   │   └── auth_response_model.dart
│   │   │   ├── repositories/
│   │   │   │   └── auth_repository.dart
│   │   │   └── services/
│   │   │       └── auth_service.dart
│   │   ├── presentation/
│   │   │   ├── pages/
│   │   │   │   ├── login_page.dart
│   │   │   │   └── register_page.dart
│   │   │   ├── widgets/
│   │   │   │   ├── login_form.dart
│   │   │   │   └── social_login_buttons.dart
│   │   │   └── providers/
│   │   │       ├── auth_provider.dart
│   │   │       └── login_form_provider.dart
│   │   └── domain/
│   │       ├── entities/
│   │       │   └── user_entity.dart
│   │       └── usecases/
│   │           ├── login_usecase.dart
│   │           └── logout_usecase.dart
│   └── [other_features]/
└── shared/
    ├── models/
    ├── services/
    │   └── supabase_service.dart
    └── providers/
        └── app_providers.dart
```

### Widget Composition Patterns
- **Atomic Design**: Build complex UIs from simple, reusable widgets
- **Single Responsibility**: One widget per file, focused on single purpose
- **Composition over Inheritance**: Prefer widget composition over extending widgets
- **Const Constructors**: Use const constructors for performance optimization

### State Management Architecture with Riverpod
- **Code Generation**: Use `@riverpod` annotations for type-safe state management
- **Provider Composition**: Complex state through provider dependencies
- **Automatic Caching**: Built-in caching with invalidation strategies
- **Repository Pattern**: Data access abstraction through repositories

### Data Flow Architecture
```
UI Layer (Widgets) 
    ↓ (user interactions)
Business Logic Layer (Riverpod Providers)
    ↓ (data requests)
Data Layer (Repositories)
    ↓ (local/remote data)
Storage Layer (Drift + Supabase)
```

---

## Modern Flutter 2025+ Standards

All applications must use these modern Flutter packages and latest dependency versions. Ensure you do not use deprecated/older versions of any packages. Always use the latest versions of any packages. Use Context7 MCP for latest APIs and patterns.

### Mandatory Modern Practices

1. **Use Riverpod Code Generation**: Always use `@riverpod` instead of `StateNotifier`
2. **Use Freezed for Models**: All data models must use Freezed for immutability
3. **Material Design 3**: Use only Material 3 components
4. **Modern Color API**: Use `Color.withValues()` instead of deprecated `Color.withOpacity()`
5. **Avoid Deprecated APIs**: No deprecated Flutter methods allowed

### Example Modern Widget

```dart
@riverpod
class LoginFormNotifier extends _$LoginFormNotifier {
  @override
  LoginFormState build() {
    return const LoginFormState();
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email);
  }
}

@freezed
class LoginFormState with _$LoginFormState {
  const factory LoginFormState({
    @Default('') String email,
    @Default('') String password,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _LoginFormState;
}
```

### Modern Flutter 2025+ Requirements
```dart
// ✅ CORRECT - Modern color management
final transparentColor = Colors.blue.withValues(alpha: 0.5);

// ✅ CORRECT - Material Design 3 components
FilledButton(onPressed: () {}, child: Text('Submit'))
Card.filled(child: ListTile(...))
NavigationBar(destinations: [...])

// ✅ CORRECT - Modern widget patterns
const SizedBox.shrink() // For empty widgets
const Divider(height: 1)
```

---

## Code Quality Standards

### NO Hardcoded Values
1. **Values of colors, AppSizings, fontsizes etc.** should be defined in the theme file.
2. No hardcoded values anywhere in the code.

### File Organization Rules
1. **One Widget Per File**: Each widget must be in its own dedicated file
2. **Maximum File Length**: 200 lines (excluding imports and comments)
3. **Maximum Function/Method Length**: 50 lines
4. **Clear Layer Separation**:
   - `data/`: Models, repositories, services (external data sources)
   - `domain/`: Entities, use cases (business logic)
   - `presentation/`: Pages, widgets, providers (UI layer)

---

## FLUTTER CODE ORGANIZATION

### Widget Organization Standards
- **One Widget Per File**: Each widget in its own file for maintainability
- **File Size Limit**: Maximum 200 lines per file
- **Function Size Limit**: Maximum 50 lines per function
- **Widget Documentation**: Comprehensive documentation for all public widgets

### Dependency Management
- **Modern Packages**: Use latest versions of all dependencies
- **Package Manager**: Use `flutter pub add` instead of manual pubspec.yaml editing
- **Version Constraints**: Use compatible version ranges for stability

### Separation of Concerns
- **Data Layer**: Models, repositories, data sources
- **Domain Layer**: Business logic, use cases, entities
- **Presentation Layer**: Widgets, screens, UI state management

---

## Naming Conventions

### File Naming
- **Files**: `snake_case.dart` (e.g., `user_profile_page.dart`)
- **Directories**: `snake_case` (e.g., `user_management/`)

### Class Naming
- **Widgets**: `PascalCase` ending with widget type (e.g., `UserProfilePage`, `LoginForm`)
- **Models**: `PascalCase` ending with `Model` (e.g., `UserModel`, `AuthResponseModel`)
- **Providers**: `PascalCase` ending with `Provider` or `Notifier` (e.g., `AuthProvider`, `LoginFormNotifier`)
- **Services**: `PascalCase` ending with `Service` (e.g., `AuthService`, `SupabaseService`)
- **Repositories**: `PascalCase` ending with `Repository` (e.g., `UserRepository`)

### Variable and Function Naming
- **Variables**: `camelCase` (e.g., `userName`, `isLoading`)
- **Functions**: `camelCase` with descriptive verbs (e.g., `validateEmail()`, `handleLogin()`)
- **Constants**: `camelCase` for local, `SCREAMING_SNAKE_CASE` for global (e.g., `API_BASE_URL`)
- **Private members**: Prefix with underscore (e.g., `_handleSubmit()`, `_isValid`)

### Widget Naming Patterns
- **Pages**: `[Feature]Page` (e.g., `LoginPage`, `DashboardPage`)
- **Forms**: `[Feature]Form` (e.g., `LoginForm`, `ProfileForm`)
- **Cards**: `[Feature]Card` (e.g., `UserCard`, `JourneyCard`)
- **Dialogs**: `[Feature]Dialog` (e.g., `ConfirmDialog`, `ErrorDialog`)
- **Bottom Sheets**: `[Feature]BottomSheet` (e.g., `FilterBottomSheet`)

### Provider Naming Patterns
- **State Providers**: `[Feature]Provider` (e.g., `authProvider`, `userProvider`)
- **Notifier Providers**: `[Feature]NotifierProvider` (e.g., `loginFormNotifierProvider`)
- **Future Providers**: `[Feature]FutureProvider` (e.g., `userDataFutureProvider`)
- **Stream Providers**: `[Feature]StreamProvider` (e.g., `authStateStreamProvider`)

---

## FLUTTER DESIGN PATTERNS

### Modern Riverpod Patterns (2025+)
```dart
// ✅ CORRECT - Modern Riverpod with code generation
@riverpod
class TaskList extends _$TaskList {
  @override
  Future<List<Task>> build() async {
    final repository = ref.watch(taskRepositoryProvider);
    return repository.getAllTasks();
  }

  Future<void> addTask(Task task) async {
    final repository = ref.read(taskRepositoryProvider);
    await repository.insertTask(task);
    ref.invalidateSelf();
  }
}

// Family providers for parameterized state
@riverpod
Future<Task?> task(TaskRef ref, String id) async {
  final repository = ref.watch(taskRepositoryProvider);
  return repository.getTaskById(id);
}

// Stream providers for real-time updates
@riverpod
Stream<List<Task>> taskStream(TaskStreamRef ref) {
  final repository = ref.watch(taskRepositoryProvider);
  return repository.watchTasks();
}
```

### Repository Pattern Implementation
```dart
@riverpod
TaskRepository taskRepository(TaskRepositoryRef ref) {
  final database = ref.watch(databaseProvider);
  final supabase = ref.watch(supabaseServiceProvider);
  return TaskRepository(database, supabase);
}

class TaskRepository {
  final AppDatabase _database;
  final SupabaseService _supabase;

  TaskRepository(this._database, this._supabase);

  Future<List<Task>> getAllTasks() async {
    // Local-first: try local database first
    final localTasks = await _database.getAllTasks();
    
    // Sync with remote if needed
    await _syncWithRemote();
    
    return localTasks;
  }
}
```

### Navigation and Routing Patterns
- **GoRouter**: Declarative routing with type-safe navigation
- **Shell Routes**: Nested navigation for complex layouts
- **Deep Linking**: Support for web URLs and mobile deep links
- **Route Guards**: Authentication and permission-based navigation

### Drift Database Patterns
- **Local Storage**: Use Drift for local data storage
- **Type Safety**: Compile-time verified SQL queries
- **Migration Management**: Handle database schema changes
- **Offline-First**: Local database as primary data source

---

## Error Handling

### Repository Level Error Handling
```dart
@riverpod
class UserRepository extends _$UserRepository {
  @override
  FutureOr<void> build() {}

  Future<Result<User, AppError>> getUser(String id) async {
    try {
      final response = await supabase.from('users').select().eq('id', id).single();
      return Result.success(User.fromJson(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(e.message));
    } catch (e) {
      return Result.failure(AppError.unknown(e.toString()));
    }
  }
}
```

### Provider Level Error Handling
```dart
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  AsyncValue<User?> build() {
    return const AsyncValue.data(null);
  }

  Future<void> loadUser(String id) async {
    state = const AsyncValue.loading();

    final result = await ref.read(userRepositoryProvider).getUser(id);

    state = result.when(
      success: (user) => AsyncValue.data(user),
      failure: (error) => AsyncValue.error(error, StackTrace.current),
    );
  }
}
```

### UI Level Error Handling
```dart
class UserProfilePage extends ConsumerWidget {
  const UserProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userNotifierProvider);

    return Scaffold(
      body: userState.when(
        data: (user) => user != null
          ? UserProfileContent(user: user)
          : const EmptyStateWidget(),
        loading: () => const LoadingWidget(),
        error: (error, stack) => ErrorWidget.withDetails(
          message: error.toString(),
          onRetry: () => ref.read(userNotifierProvider.notifier).loadUser('id'),
        ),
      ),
    );
  }
}
```

### Validation Patterns

#### Form Validation
```dart
class Validators {
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    return null;
  }
}
```

#### Input Validation in Providers
```dart
@riverpod
class LoginFormNotifier extends _$LoginFormNotifier {
  @override
  LoginFormState build() {
    return const LoginFormState();
  }

  void updateEmail(String email) {
    final emailError = Validators.email(email);
    state = state.copyWith(
      email: email,
      emailError: emailError,
      isValid: emailError == null && state.passwordError == null,
    );
  }
}
```

### State Management Best Practices

#### Provider Organization
```dart
// Group related providers in a single file
// auth_providers.dart
@riverpod
SupabaseClient supabase(SupabaseRef ref) {
  return Supabase.instance.client;
}

@riverpod
Stream<AuthState> authStateStream(AuthStateStreamRef ref) {
  return ref.watch(supabaseProvider).auth.onAuthStateChange;
}

@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AsyncValue<User?> build() {
    final authStream = ref.watch(authStateStreamProvider);
    return authStream.when(
      data: (authState) => AsyncValue.data(authState.session?.user),
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    );
  }
}
```

#### Provider Dependencies
```dart
@riverpod
class JourneyNotifier extends _$JourneyNotifier {
  @override
  AsyncValue<List<Journey>> build() {
    // Depend on auth state
    final user = ref.watch(authNotifierProvider).value;
    if (user == null) {
      return const AsyncValue.data([]);
    }

    return ref.watch(journeyRepositoryProvider).getUserJourneys(user.id);
  }
}
```

---

## Cross-Platform Considerations

### Platform Detection
```dart
// core/utils/platform_utils.dart
class PlatformUtils {
  static bool get isWeb => kIsWeb;
  static bool get isMobile => !kIsWeb && (Platform.isIOS || Platform.isAndroid);
  static bool get isDesktop => !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);

  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;
}
```

### Responsive Design Patterns

#### Breakpoint Constants
```dart
// core/constants/ui_constants.dart
class BreakPoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
}

class ResponsiveUtils {
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < BreakPoints.mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= BreakPoints.mobile && width < BreakPoints.desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= BreakPoints.desktop;
  }
}
```

#### Responsive Widget Pattern
```dart
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    if (ResponsiveUtils.isDesktop(context)) {
      return desktop;
    } else if (ResponsiveUtils.isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}
```

### Supabase Integration Patterns

#### Shared Supabase Service
```dart
// shared/services/supabase_service.dart
@riverpod
SupabaseClient supabase(SupabaseRef ref) {
  return Supabase.instance.client;
}

class SupabaseService {
  final SupabaseClient _client;

  SupabaseService(this._client);

  // Auth methods
  Future<AuthResponse> signInWithEmail(String email, String password) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<AuthResponse> signUp(String email, String password) async {
    return await _client.auth.signUp(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  // Database methods
  PostgrestFilterBuilder<T> from<T>(String table) {
    return _client.from(table);
  }
}
```

#### Platform-Specific Auth Configuration
```dart
// For Web App (meditatingleo_webapp)
class WebAuthConfig {
  static const redirectUrl = 'https://yourapp.com/auth/callback';
  static const googleClientId = 'your-google-client-id';
}

// For Mobile App (meditatingleo_app)
class MobileAuthConfig {
  static const redirectUrl = 'com.yourapp.meditatingleo://auth/callback';
  static const googleClientId = 'your-mobile-google-client-id';
}

// For Admin Panel (meditatingleo_admin)
class AdminAuthConfig {
  static const redirectUrl = 'https://admin.yourapp.com/auth/callback';
  static const googleClientId = 'your-admin-google-client-id';
}
```

---

## Testing Standards

### Test Organization
```
test/
├── unit/
│   ├── models/
│   ├── repositories/
│   ├── services/
│   └── utils/
├── widget/
│   ├── pages/
│   ├── widgets/
│   └── forms/
└── integration/
    ├── auth_flow_test.dart
    └── journey_flow_test.dart
```

### Unit Test Template
```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import 'package:meditatingleo_app/features/auth/data/models/user_model.dart';
import 'package:meditatingleo_app/features/auth/data/repositories/auth_repository.dart';

// Generate mocks
import 'auth_repository_test.mocks.dart';

void main() {
  group('AuthRepository', () {
    late AuthRepository authRepository;
    late MockSupabaseClient mockSupabaseClient;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      authRepository = AuthRepository(mockSupabaseClient);
    });

    group('signIn', () {
      test('should return user when sign in is successful', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final mockResponse = AuthResponse(/* mock data */);

        when(mockSupabaseClient.auth.signInWithPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockResponse);

        // Act
        final result = await authRepository.signIn(email, password);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, isA<User>());
        verify(mockSupabaseClient.auth.signInWithPassword(
          email: email,
          password: password,
        )).called(1);
      });

      test('should return error when sign in fails', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(mockSupabaseClient.auth.signInWithPassword(
          email: email,
          password: password,
        )).thenThrow(AuthException('Invalid credentials'));

        // Act
        final result = await authRepository.signIn(email, password);

        // Assert
        expect(result.isFailure, true);
        expect(result.error, isA<AuthError>());
      });
    });
  });
}
```

### Widget Test Template
```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:meditatingleo_app/features/auth/presentation/pages/login_page.dart';
import 'package:meditatingleo_app/features/auth/presentation/providers/auth_provider.dart';

void main() {
  group('LoginPage', () {
    testWidgets('should display login form', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginPage(),
          ),
        ),
      );

      // Act & Assert
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('should show error when invalid email is entered', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginPage(),
          ),
        ),
      );

      // Act
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });
  });
}
```

---

## Performance Guidelines

### Widget Performance

#### Use const constructors
```dart
// Good
const Text('Hello World')

// Bad
Text('Hello World')
```

#### Avoid rebuilds with proper provider usage
```dart
// Good - Only rebuilds when specific data changes
final userName = ref.watch(userProvider.select((user) => user?.name));

// Bad - Rebuilds when any user data changes
final user = ref.watch(userProvider);
final userName = user?.name;
```

#### Use ListView.builder for large lists
```dart
// Good
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ItemWidget(items[index]),
)

// Bad
ListView(
  children: items.map((item) => ItemWidget(item)).toList(),
)
```

### Memory Management

#### Dispose controllers and streams
```dart
class _MyWidgetState extends State<MyWidget> {
  late final TextEditingController _controller;
  late final StreamSubscription _subscription;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _subscription = someStream.listen(handleData);
  }

  @override
  void dispose() {
    _controller.dispose();
    _subscription.cancel();
    super.dispose();
  }
}
```

#### Use AutoDispose for providers
```dart
// Good - Automatically disposed when not used
@riverpod
Future<List<Item>> items(ItemsRef ref) async {
  // Implementation
}

// For providers that should stay alive
@Riverpod(keepAlive: true)
Future<AppConfig> appConfig(AppConfigRef ref) async {
  // Implementation
}
```

---

## Security Guidelines

### Data Validation
```dart
class SecurityUtils {
  /// Sanitizes user input to prevent XSS attacks
  static String sanitizeInput(String input) {
    return input
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('&', '&amp;');
  }

  /// Validates that a string contains only safe characters
  static bool isSafeString(String input) {
    final safePattern = RegExp(r'^[a-zA-Z0-9\s\-_.,!?]+$');
    return safePattern.hasMatch(input);
  }
}
```

### Supabase Security
```dart
// Always use Row Level Security (RLS) policies
// Never expose sensitive data in client-side code
// Use environment variables for configuration

class SupabaseConfig {
  static const String url = String.fromEnvironment('SUPABASE_URL');
  static const String anonKey = String.fromEnvironment('SUPABASE_ANON_KEY');

  // Never hardcode these values!
  static void validateConfig() {
    assert(url.isNotEmpty, 'SUPABASE_URL must be provided');
    assert(anonKey.isNotEmpty, 'SUPABASE_ANON_KEY must be provided');
  }
}
```

---

## Documentation Standards

### Code Comments

#### Class Documentation
```dart
/// [UserRepository] handles all user-related data operations.
///
/// This repository provides methods for:
/// - Fetching user data from Supabase
/// - Updating user profiles
/// - Managing user preferences
///
/// Example usage:
/// ```dart
/// final userRepo = ref.read(userRepositoryProvider);
/// final user = await userRepo.getUser('user-id');
/// ```
class UserRepository {
  // Implementation
}
```

#### Method Documentation
```dart
/// Validates an email address format.
///
/// Returns `null` if the email is valid, otherwise returns an error message.
///
/// Parameters:
/// - [email]: The email address to validate
///
/// Returns:
/// - `null` if valid
/// - Error message string if invalid
///
/// Example:
/// ```dart
/// final error = Validators.email('<EMAIL>'); // Returns null
/// final error = Validators.email('invalid'); // Returns error message
/// ```
static String? validateEmail(String? email) {
  // Implementation
}
```

#### Complex Logic Comments
```dart
void _calculateJourneyProgress() {
  // Calculate completion percentage based on completed prompts
  // Formula: (completed_prompts / total_prompts) * 100
  final completionRate = completedPrompts / totalPrompts;

  // Apply bonus multiplier for consecutive days (max 1.2x)
  final consecutiveBonus = math.min(consecutiveDays * 0.1, 0.2);
  final adjustedRate = completionRate * (1 + consecutiveBonus);

  // Ensure the rate doesn't exceed 100%
  progress = math.min(adjustedRate, 1.0);
}
```

### Widget File Template
```dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/feature_provider.dart';

/// [FeatureWidget] description of what this widget does.
///
/// Used in: List the main places where this widget is used
///
/// Example:
/// ```dart
/// FeatureWidget(
///   parameter: value,
/// )
/// ```
class FeatureWidget extends ConsumerWidget {
  /// Brief description of the parameter
  final String parameter;

  const FeatureWidget({
    super.key,
    required this.parameter,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(featureProvider);

    return Container(
      // Widget implementation
    );
  }
}
```

### Model File Template
```dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'model_name.freezed.dart';
part 'model_name.g.dart';

/// [ModelName] represents...
///
/// Used for: Describe the purpose and usage
@freezed
class ModelName with _$ModelName {
  /// Creates a [ModelName] instance.
  const factory ModelName({
    required String id,
    required String name,
    @Default(false) bool isActive,
    DateTime? createdAt,
  }) = _ModelName;

  /// Creates a [ModelName] from JSON.
  factory ModelName.fromJson(Map<String, dynamic> json) =>
      _$ModelNameFromJson(json);
}
```

### Provider File Template
```dart
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../shared/services/supabase_service.dart';
import '../data/repositories/feature_repository.dart';
import '../data/models/feature_model.dart';

part 'feature_provider.g.dart';

/// [FeatureNotifier] manages the state for...
///
/// Provides: List what this provider provides
/// Depends on: List dependencies
@riverpod
class FeatureNotifier extends _$FeatureNotifier {
  @override
  AsyncValue<FeatureState> build() {
    return const AsyncValue.data(FeatureState.initial());
  }

  /// Method description
  Future<void> methodName() async {
    state = const AsyncValue.loading();

    try {
      // Implementation
      state = AsyncValue.data(newState);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

/// Simple provider for dependency injection
@riverpod
FeatureRepository featureRepository(FeatureRepositoryRef ref) {
  return FeatureRepository(ref.watch(supabaseServiceProvider));
}
```

### README Standards

Each application must have a comprehensive README.md:

```markdown
# MeditatingLeo [App/WebApp/Admin]

## Overview
Brief description of the application's purpose and target users.

## Features
- List of main features
- Organized by user type or functionality

## Getting Started

### Prerequisites
- Flutter 3.5.3+
- Dart 3.5.3+
- Supabase account

### Installation
1. Clone the repository
2. Install dependencies: `flutter pub get`
3. Run code generation: `flutter packages pub run build_runner build`
4. Configure Supabase (see Configuration section)
5. Run the app: `flutter run`

### Configuration
Details about environment setup, API keys, etc.

## Architecture
Brief overview of the app's architecture and main patterns used.

## Contributing
Guidelines for contributing to this specific application.
```

---

## Import Organization

### Order of Imports
1. Dart core libraries
2. Flutter libraries
3. Third-party packages
4. Local imports (relative paths)

```dart
// 1. Dart core
import 'dart:async';
import 'dart:convert';

// 2. Flutter
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party packages
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// 4. Local imports
import '../../../core/constants/app_constants.dart';
import '../../../shared/models/user_model.dart';
import '../providers/auth_provider.dart';

// Generated files (always last)
part 'login_page.freezed.dart';
part 'login_page.g.dart';
```

---

## FLUTTER COMPONENT RELATIONSHIPS

### Widget Communication Patterns
- **Provider Dependencies**: Widgets communicate through shared Riverpod providers
- **Callback Functions**: Parent-child communication through callback parameters
- **Event Bus**: Global events through Riverpod providers for loose coupling

### Data Passing Conventions
- **Constructor Parameters**: Direct data passing for simple cases
- **Provider Watching**: Reactive data access through Riverpod
- **Context Extensions**: Convenient access to common data

### Riverpod Provider Dependencies
```dart
@riverpod
class UserProfile extends _$UserProfile {
  @override
  Future<UserProfileState> build(String userId) async {
    // Depend on other providers
    final authState = ref.watch(authStateProvider);
    final userRepo = ref.watch(userRepositoryProvider);

    if (!authState.isAuthenticated) {
      throw UnauthorizedException();
    }

    final user = await userRepo.getUser(userId);
    return UserProfileState(user: user);
  }
}
```

### Event Handling Patterns
- **User Interactions**: Handle through widget callbacks and provider methods
- **System Events**: Lifecycle events through provider lifecycle methods
- **Background Events**: Handle through platform channels and services

---

## FLUTTER ANTI-PATTERNS TO AVOID

### Widget Hierarchy Anti-Patterns
```dart
// ❌ AVOID - Overly nested widget trees
Widget build(BuildContext context) {
  return Scaffold(
    body: Container(
      child: Padding(
        child: Column(
          children: [
            Container(
              child: Row(
                children: [
                  Container(child: Text('...')), // Too much nesting
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

// ✅ CORRECT - Extract widgets for better composition
Widget build(BuildContext context) {
  return Scaffold(
    body: Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _HeaderSection(),
          _ContentSection(),
        ],
      ),
    ),
  );
}
```

### State Management Anti-Patterns
- **Global State Abuse**: Don't put everything in global state
- **Unnecessary Providers**: Avoid creating providers for simple local state
- **Provider Pollution**: Don't create too many fine-grained providers
- **State Mutation**: Never mutate state directly, always create new instances

### Performance Anti-Patterns
- **Unnecessary Rebuilds**: Avoid rebuilding widgets unnecessarily
- **Heavy Build Methods**: Don't perform expensive operations in build methods
- **Memory Leaks**: Properly dispose of controllers and subscriptions
- **Blocking UI**: Don't perform long-running operations on the main thread

### Deprecated API Usage
```dart
// ❌ AVOID - Deprecated APIs
Color.withOpacity(0.5)  // Use Color.withValues(alpha: 0.5)
RaisedButton(...)       // Use FilledButton(...)
FlatButton(...)         // Use TextButton(...)
```

## MOBILE-SPECIFIC PATTERNS

### Responsive Design Patterns
- **LayoutBuilder**: Adapt layout based on available space
- **MediaQuery**: Access device dimensions and capabilities
- **Breakpoints**: Define consistent breakpoints for different screen sizes
- **Adaptive Widgets**: Use platform-appropriate widgets

### Platform Adaptation Patterns
```dart
// Platform-specific adaptations
Widget build(BuildContext context) {
  return Platform.isIOS 
    ? CupertinoButton(child: Text('iOS Button'))
    : FilledButton(child: Text('Android Button'));
}

// Theme-based adaptations
Widget build(BuildContext context) {
  final theme = Theme.of(context);
  return theme.platform == TargetPlatform.iOS
    ? CupertinoNavigationBar()
    : AppBar();
}
```

### Background Processing Patterns
- **Isolates**: Use isolates for CPU-intensive tasks
- **Background Tasks**: Handle background processing appropriately
- **Lifecycle Management**: Properly handle app lifecycle events

### Offline-First Data Patterns
- **Local Database**: Use Drift for local data storage
- **Sync Strategies**: Implement robust sync between local and remote data
- **Conflict Resolution**: Handle data conflicts gracefully
- **Cache Management**: Implement intelligent caching strategies

### Push Notification Patterns
- **Permission Handling**: Request permissions appropriately
- **Message Processing**: Handle different types of notifications
- **Deep Linking**: Navigate to appropriate screens from notifications
- **Background Handling**: Process notifications when app is backgrounded

---

---

## Conclusion

This document serves as the definitive guide for Flutter development across all MeditatingLeo applications. All developers must:

1. **Follow the architecture patterns** outlined in this document
2. **Use modern Flutter 2025+ standards** exclusively
3. **Maintain consistency** across all three applications
4. **Write comprehensive tests** for all new features
5. **Document code** according to the standards
6. **Review this document regularly** as it may be updated

For questions or clarifications about these standards, refer to the project documentation or discuss with the development team.

**Remember**: Each application (`meditatingleo_app`, `meditatingleo_webapp`, `meditatingleo_admin`) must remain completely independent with no shared packages, but should follow identical coding patterns and standards as defined in this document.

---

## Document Maintenance

This document should be updated when:
- New architectural patterns are established
- Flutter framework updates require pattern changes
- New anti-patterns are identified
- Performance optimizations are discovered
- Accessibility requirements change

**Last Updated**: January 2025
**Next Review**: Quarterly or when Flutter framework updates significantly
